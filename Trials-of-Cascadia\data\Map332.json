{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "EG_Unknown_Loop", "pan": 0, "pitch": 100, "volume": 30}, "bgs": {"name": "Rain_Heavy_Loop", "pan": 0, "pitch": 100, "volume": 40}, "disableDashing": true, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "<Zoom: 200%>\n<Hide Compass>\n<Fog 1 Settings>\n Name: !wispy_white_clouds\n Opacity: 50\n Horz Scroll: 0.0\nColor Tone: 0, 0, 0, 255\n</Fog 1 Settings>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 52, "width": 17, "data": [3248, 3252, 3276, 3276, 3276, 3276, 3276, 3276, 3276, 3276, 3256, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3272, 5123, 5122, 5122, 5122, 5122, 5122, 5122, 5126, 3264, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3272, 5129, 5128, 5128, 5128, 5128, 5128, 5128, 5132, 3288, 3276, 3276, 3276, 3276, 3256, 3248, 3248, 3272, 6834, 6820, 6820, 6820, 6820, 6820, 6820, 6836, 5123, 5122, 5122, 5122, 5126, 3264, 3248, 3248, 3272, 6816, 6800, 6800, 6800, 6800, 6800, 6800, 6824, 5129, 5128, 5128, 5128, 5132, 3264, 3248, 3248, 3272, 6816, 6800, 6800, 6800, 6800, 6800, 6800, 6802, 6820, 6820, 6820, 6820, 6836, 3264, 3248, 3248, 3272, 6816, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6824, 3264, 3248, 3248, 3272, 6816, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6800, 6824, 3264, 3248, 3248, 3272, 6840, 6828, 6828, 6828, 6828, 6808, 6800, 6800, 6800, 6800, 6800, 6800, 6824, 3264, 3248, 3248, 3250, 3268, 3268, 3268, 3268, 3284, 6816, 6800, 6800, 6800, 6800, 6800, 6800, 6824, 3264, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3272, 6840, 6828, 6828, 6828, 6828, 6828, 6828, 6838, 3264, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3250, 3268, 3268, 3268, 3268, 3268, 3268, 3268, 3268, 3249, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 3248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3420, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3412, 3412, 3428, 0, 0, 0, 0, 3426, 3412, 3412, 3412, 3428, 0, 0, 0, 3408, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3416, 0, 0, 0, 3432, 3420, 3420, 3420, 3430, 0, 0, 0, 0, 3432, 3420, 3420, 3420, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 433, 408, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 441, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 312, 313, 0, 0, 0, 433, 434, 435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 320, 321, 0, 0, 0, 441, 442, 443, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 468, 614, 0, 0, 0, 0, 0, 0, 400, 0, 0, 0, 0, 0, 0, 0, 0, 476, 269, 0, 0, 0, 0, 0, 573, 434, 435, 0, 0, 0, 0, 0, 0, 0, 484, 0, 0, 0, 0, 0, 0, 581, 442, 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 107, 107, 107, 107, 107, 107, 107, 107, 0, 107, 107, 107, 107, 107, 107, 107, 107], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Characters/!Fantasy_door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 101, "indent": 1, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["It's locked.. I'm trapped in this room! I have"]}, {"code": 401, "indent": 1, "parameters": ["to find a way out!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["It's locked.."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 2}, {"id": 2, "name": "EV002", "note": "<Sprite Offset Y: -24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 2, "characterIndex": 4}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Emergency contacts:<br>"]}, {"code": 401, "indent": 0, "parameters": ["Police: 555-0123<br>"]}, {"code": 401, "indent": 0, "parameters": ["Fire Dept: 555-0124<br>"]}, {"code": 401, "indent": 0, "parameters": ["Hospital: 555-0125<br>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 2}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "Rain_Heavy_Loop", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Dark Grey", "Duration:num": "0"}]}, {"code": 657, "indent": 1, "parameters": ["Color = Dark Grey"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON>_With_Distant_Animal_Crying_Loop", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Grey", "Duration:num": "0"}]}, {"code": 657, "indent": 1, "parameters": ["Color = Grey"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [119, 119, 0, 0, 1]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What the...\\| what is this place?"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "Rain_Heavy_Loop", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Dark Grey", "Duration:num": "0"}]}, {"code": 657, "indent": 1, "parameters": ["Color = Dark Grey"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON>_With_Distant_Animal_Crying_Loop", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Grey", "Duration:num": "0"}]}, {"code": 657, "indent": 1, "parameters": ["Color = Grey"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Grey>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 400>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 120, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: White>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 400>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 6}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$!phone", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What a strange device.. I've never seen this"]}, {"code": 401, "indent": 0, "parameters": ["kind of technology."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Pick it up?"]}, {"code": 102, "indent": 0, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Yes"]}, {"code": 103, "indent": 1, "parameters": [139, 7]}, {"code": 111, "indent": 1, "parameters": [1, 139, 0, 5550123, 0]}, {"code": 101, "indent": 2, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 2, "parameters": ["No answer..."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 139, 0, 5550124, 0]}, {"code": 101, "indent": 2, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 2, "parameters": ["No answer..."]}, {"code": 357, "indent": 2, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "SelfSwitchABCD", "Self Switch: A B C D", {"MapId:eval": "0", "EventId:eval": "18", "Letter:str": "A", "Break": "", "Value:str": "ON"}]}, {"code": 657, "indent": 2, "parameters": ["Map ID = 0"]}, {"code": 657, "indent": 2, "parameters": ["Event ID = 18"]}, {"code": 657, "indent": 2, "parameters": ["Letter = A"]}, {"code": 657, "indent": 2, "parameters": ["- = "]}, {"code": 657, "indent": 2, "parameters": ["Value = ON"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 139, 0, 5550125, 0]}, {"code": 101, "indent": 2, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 2, "parameters": ["No answer..."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 139, 0, 5550341, 0]}, {"code": 101, "indent": 2, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 2, "parameters": ["No answer..."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 139, 0, 5550567, 0]}, {"code": 101, "indent": 2, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 2, "parameters": ["No answer..."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 139, 0, 5550777, 0]}, {"code": 101, "indent": 2, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 2, "parameters": ["No answer..."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 139, 1, 11, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 2, "parameters": ["Correct!"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "No"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 160, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$!phone", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 50>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$!phone", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 50>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 121, "indent": 0, "parameters": [160, 160, 1]}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "SelfSwitchABCD", "Self Switch: A B C D", {"MapId:eval": "0", "EventId:eval": "16", "Letter:str": "A", "Break": "", "Value:str": "ON"}]}, {"code": 657, "indent": 0, "parameters": ["Map ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 16"]}, {"code": 657, "indent": 0, "parameters": ["Letter = A"]}, {"code": 657, "indent": 0, "parameters": ["- = "]}, {"code": 657, "indent": 0, "parameters": ["Value = ON"]}, {"code": 122, "indent": 0, "parameters": [138, 138, 0, 2, 0, 5]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["H-Hello?! Who's there??"]}, {"code": 0, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(138) == 0"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["Thank you for calling customer service."]}, {"code": 401, "indent": 1, "parameters": ["Please hold while we connect you to...—*static*"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(138) == 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["Thank you for calling! The weather today will be...—*static*"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(138) == 2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["This is a test of the emergency broadcast"]}, {"code": 401, "indent": 1, "parameters": ["system. This is only a test."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(138) == 3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["You have one new message:\\|"]}, {"code": 401, "indent": 1, "parameters": ["'Dad? Dad! Are you there? Can you hear me??'"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(138) == 4"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["<PERSON>?\\| <PERSON>, is that you?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(138) == 5"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["This number is no longer in service.\\|"]}, {"code": 401, "indent": 1, "parameters": ["But there are other numbers that are."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What... what was that?!"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 3, "walkAnime": true}], "x": 6, "y": 2}, {"id": 6, "name": "Ghost", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Characters/SF_Monster", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["<Ambience SFX: devil3>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience Proximity: 10>"]}, {"code": 353, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["Move to Player"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 5, "y": 4}, {"id": 7, "name": "Bed", "note": "<hitbox up: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Close your eyes for a while?"]}, {"code": 102, "indent": 0, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Yes"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "SelfSwitchABCD", "Self Switch: A B C D", {"MapId:eval": "0", "EventId:eval": "16", "Letter:str": "A", "Break": "", "Value:str": "OFF"}]}, {"code": 657, "indent": 1, "parameters": ["Map ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 16"]}, {"code": 657, "indent": 1, "parameters": ["Letter = A"]}, {"code": 657, "indent": 1, "parameters": ["- = "]}, {"code": 657, "indent": 1, "parameters": ["Value = OFF"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 45, "parameters": ["Teleport to: 13,6"], "indent": null}, {"code": 45, "parameters": ["Pose: Sleep0"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 45, "parameters": ["Teleport to: 13,6"], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 45, "parameters": ["Pose: Sleep0"], "indent": null}]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer._sprite.y -= 24;"]}, {"code": 122, "indent": 1, "parameters": [120, 120, 0, 2, 0, 1]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 245, "indent": 2, "parameters": [{"name": "Rain_Heavy_Loop", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 245, "indent": 2, "parameters": [{"name": "<PERSON>_With_Distant_Animal_Crying_Loop", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [119, 119, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [160, 160, 1]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<Center>Day \\\\v[119]\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"432\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"true\",\"SoundFilename:str\":\"\",\"SoundVolume:num\":\"90\",\"SoundPitch:num\":\"100\",\"SoundPan:num\":\"0\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<Center>Day \\\\v[119]\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [180], "indent": null}, {"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [180], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 111, "indent": 1, "parameters": [1, 119, 0, 2, 0]}, {"code": 101, "indent": 2, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 2, "parameters": ["I need to find a way out of this strange room."]}, {"code": 401, "indent": 2, "parameters": ["Maybe I should have a look around."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "No"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [999]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 13, "y": 6}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!clock", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Clock1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 1, 1]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!clock", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Clock2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 1, 1]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!clock", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Clock1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 1, 1]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!clock", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Clock2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 1, 1]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}], "x": 9, "y": 2}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Grey>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 40%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 40%>"]}, {"code": 201, "indent": 0, "parameters": [0, 333, 0, 17, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 120, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: White>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 40%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 40%>"]}, {"code": 201, "indent": 0, "parameters": [0, 333, 0, 17, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 4}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 639, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Technical Specifications:<br>"]}, {"code": 401, "indent": 0, "parameters": ["Voltage: 12.7V DC<br>"]}, {"code": 401, "indent": 0, "parameters": ["Current: 2.4A ±0.1<br>"]}, {"code": 401, "indent": 0, "parameters": ["Frequency: 60Hz<br>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 3}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 631, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 2}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 4, "pattern": 1, "characterIndex": 3}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Sarah: 555-0341<br>"]}, {"code": 401, "indent": 0, "parameters": ["Michael: 555-0567<br>"]}, {"code": 401, "indent": 0, "parameters": ["Maintenance: 555-0777<br>"]}, {"code": 401, "indent": 0, "parameters": ["Call for repairs or leaks."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 2}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Decoration_static", "direction": 8, "pattern": 2, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 7}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Decoration_static", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["A framed picture... there's something written on the back."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["\"Patience is a virtue\""]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 4}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$!phone", "direction": 6, "pattern": 0, "characterIndex": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 14, "y": 3}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [999]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(119) % 3 == 0"]}, {"code": 213, "indent": 1, "parameters": [-1, 1, false]}, {"code": 121, "indent": 1, "parameters": [160, 160, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 160, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Phone", "volume": 20, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 1}, {"id": 17, "name": "EV017", "note": "<Sprite Offset Y: -12>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Decoration_desert_2", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 10%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 40%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 120, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Decoration_desert_2", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 7}, {"id": 18, "name": "Fire", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [999]}, {"code": 230, "indent": 0, "parameters": [999]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Red", "Duration:num": "999"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Red"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 999"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_Embers", "FIRE: Embers", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"130\\\",\\\"opacityVariance:num\\\":\\\"30\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"totalMinimum:num\\\":\\\"30\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"16\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"16\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"2\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"4\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 245, "indent": 0, "parameters": [{"name": "Forest_Fire", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [333]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_FlameWall", "FIRE: Flame Wall", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"2\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"90\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"130\\\",\\\"opacityVariance:num\\\":\\\"30\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.5\\\",\\\"totalMinimum:num\\\":\\\"50\\\",\\\"totalPerPower:num\\\":\\\"25\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"32\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"32\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"3\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"4\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"90\\…"]}, {"code": 230, "indent": 0, "parameters": [666]}, {"code": 353, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 2}, {"id": 19, "name": "Police", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [999]}, {"code": 230, "indent": 0, "parameters": [999]}, {"code": 230, "indent": 0, "parameters": [333]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move10", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [666]}, {"code": 353, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 3}, null]}