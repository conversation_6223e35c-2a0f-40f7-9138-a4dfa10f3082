{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "Grassland", "battleback2Name": "Grassland", "bgm": {"name": "Time-to-Fight-Further", "pan": 0, "pitch": 95, "volume": 3}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 15, "note": "<Zoom: 200%>\n<connect map: 5 0 h>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "!Forest3", "parallaxShow": true, "parallaxSx": 1, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 7, "width": 24, "data": [2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 2384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Red Kid2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 102, "indent": 0, "parameters": [["Test", "Test2", "Train", "Repeating Easy", "Repeating Hard", "Random"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Test"]}, {"code": 301, "indent": 1, "parameters": [0, 200, true, false]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Test2"]}, {"code": 301, "indent": 1, "parameters": [0, 199, true, false]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Train"]}, {"code": 301, "indent": 1, "parameters": [0, 118, true, false]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Repeating Easy"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 301, "indent": 1, "parameters": [0, 1, true, true]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 603, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 301, "indent": 1, "parameters": [0, 1, true, true]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 603, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 301, "indent": 1, "parameters": [0, 1, true, true]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 603, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "Repeating Hard"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 301, "indent": 1, "parameters": [0, 109, true, true]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 603, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 301, "indent": 1, "parameters": [0, 109, true, true]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 603, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 301, "indent": 1, "parameters": [0, 109, true, true]}, {"code": 601, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 314, "indent": 2, "parameters": [0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 602, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 603, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 604, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [5, "Random"]}, {"code": 314, "indent": 1, "parameters": [0, 0]}, {"code": 122, "indent": 1, "parameters": [120, 120, 0, 2, 0, 5]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 301, "indent": 2, "parameters": [0, 2, true, true]}, {"code": 601, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 602, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 603, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 604, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 301, "indent": 2, "parameters": [0, 4, true, true]}, {"code": 601, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 602, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 603, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 604, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 2, 0]}, {"code": 301, "indent": 2, "parameters": [0, 6, true, true]}, {"code": 601, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 602, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 603, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 604, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 3, 0]}, {"code": 301, "indent": 2, "parameters": [0, 8, true, true]}, {"code": 601, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 602, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 603, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 604, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 4, 0]}, {"code": 301, "indent": 2, "parameters": [0, 24, true, true]}, {"code": 601, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 602, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 603, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 604, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 5, 0]}, {"code": 301, "indent": 2, "parameters": [0, 28, true, true]}, {"code": 601, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 602, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 603, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 604, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 6}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 71, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Coin", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 126, "indent": 0, "parameters": [61, 0, 0, 99]}, {"code": 126, "indent": 0, "parameters": [62, 0, 0, 99]}, {"code": 126, "indent": 0, "parameters": [63, 0, 0, 99]}, {"code": 126, "indent": 0, "parameters": [64, 0, 0, 10]}, {"code": 126, "indent": 0, "parameters": [65, 0, 0, 10]}, {"code": 126, "indent": 0, "parameters": [66, 0, 0, 10]}, {"code": 126, "indent": 0, "parameters": [67, 0, 0, 10]}, {"code": 126, "indent": 0, "parameters": [68, 0, 0, 10]}, {"code": 125, "indent": 0, "parameters": [0, 0, 99999]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Got all gems x10!"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 13}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_SunBeams", "FIRE: Sunlight Beams", {"MainData": "", "powerTarget:eval": "4", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"240\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"left 50%\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"upper 10%\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"24\\\",\\\"opacityVariance:num\\\":\\\"16\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"60\\\",\\\"scale:num\\\":\\\"1.5\\\",\\\"scaleVariance:num\\\":\\\"1.4\\\",\\\"scaleRatioX:num\\\":\\\"1\\\",\\\"scaleRatioY:num\\\":\\\"0.02\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"10\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ffffff\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"6\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"1\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"0\\\",\\\"sparkleFinish:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"frozen\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"300\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"0\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 4"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"240…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Wind_GreenLeaves", "WIND: Green Leaves", {"MainData": "", "powerTarget:eval": "3", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"2\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"600\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"left 50%\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"upper border\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"255\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"InCubic\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.3\\\",\\\"scaleVariance:num\\\":\\\"0.1\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"true\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"+0\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"310\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+2.5\\\",\\\"spinSpeedVariance:eval\\\":\\\"1\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"2\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 3"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"600…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Water_Mist", "WATER: Mist", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"middle 30%\\\",\\\"spawnOffsetY:eval\\\":\\\"+150\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"128\\\",\\\"opacityVariance:num\\\":\\\"15\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"80\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"2.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#888888\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"0\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_Fireflies", "FIRE: Fireflies", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"4\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"180\\\",\\\"lifespanVariance:num\\\":\\\"30\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"240\\\",\\\"opacityVariance:num\\\":\\\"15\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"totalMinimum:num\\\":\\\"10\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"0\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"3\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"2\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"4\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"180…"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 1}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "8D people3-7", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["ToggleChoiceSelection", "ToggleChoiceHorizontal", "Toggle Choice Horizontal", {}]}, {"code": 102, "indent": 0, "parameters": [["Haventown", "Change Subclass", "????", "????", "????"], -2, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Haventown"]}, {"code": 201, "indent": 1, "parameters": [0, 2, 33, 31, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Change Subclass"]}, {"code": 117, "indent": 1, "parameters": [264]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "????"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "????"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "????"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 403, "indent": 0, "parameters": [6, null]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 6}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["Rosedale_TransitionEffects", "character_transition", "Character Transition", {"target": "9", "name": "Shadow Creature", "type": "fade_out", "wait": "true"}]}, {"code": 657, "indent": 0, "parameters": ["Character ID = 9"]}, {"code": 657, "indent": 0, "parameters": ["Name = <PERSON> Creature"]}, {"code": 657, "indent": 0, "parameters": ["Type = fade_out"]}, {"code": 657, "indent": 0, "parameters": ["Wait for Completion = true"]}, {"code": 357, "indent": 0, "parameters": ["Rosedale_TransitionEffects", "character_transition", "Character Transition", {"target": "9", "name": "Shadow Creature", "type": "fade_in", "wait": "true"}]}, {"code": 657, "indent": 0, "parameters": ["Character ID = 9"]}, {"code": 657, "indent": 0, "parameters": ["Name = <PERSON> Creature"]}, {"code": 657, "indent": 0, "parameters": ["Type = fade_in"]}, {"code": 657, "indent": 0, "parameters": ["Wait for Completion = true"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 0}, {"id": 6, "name": "Mystic Match", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "mondrus-walking2[VS8]", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Mystic Match is a game I played to keep my"]}, {"code": 401, "indent": 0, "parameters": ["memory and concentration sharp while I spent"]}, {"code": 401, "indent": 0, "parameters": ["time alone."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["You must memorize the cards before they're"]}, {"code": 401, "indent": 0, "parameters": ["turned face down, and then find the ones that"]}, {"code": 401, "indent": 0, "parameters": ["match the target card."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Each round, you will have less time to see the"]}, {"code": 401, "indent": 0, "parameters": ["cards, so you must focus!"]}, {"code": 201, "indent": 0, "parameters": [0, 217, 16, 24, 0, 0]}, {"code": 122, "indent": 0, "parameters": [213, 213, 0, 0, 1]}, {"code": 118, "indent": 0, "parameters": ["Start"]}, {"code": 355, "indent": 0, "parameters": ["for (let i = 191; i <= 199; i++) $gameVariables.setValue(i, 0);"]}, {"code": 111, "indent": 0, "parameters": [1, 190, 0, 9, 0]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Final Round\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"360\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"true\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Final Round\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Round \\\\v[190]\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"360\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"true\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Round \\\\v[190]\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [119, 119, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [130, 130, 0, 0, 0]}, {"code": 357, "indent": 0, "parameters": ["Wave 7/VisuMZ_3_VisualTextWindows", "AddChangeVisualTextWindow", "Text Window: Add/Change Settings", {"id:num": "1", "text:json": "\"Turns Left \\\\v[119]\\nCombo \\\\v[139]\"", "Customize:struct": "{\"Coordinates\":\"\",\"x:str\":\"1050\",\"y:str\":\"600\",\"width:str\":\"auto\",\"height:str\":\"auto\",\"Alignment\":\"\",\"alignX:str\":\"left\",\"alignY:str\":\"top\",\"Appear\":\"\",\"autoColor:eval\":\"false\",\"appearType:str\":\"open\",\"Background\":\"\",\"bgType:num\":\"0\",\"opacity:eval\":\"192\"}"}]}, {"code": 657, "indent": 0, "parameters": ["ID = 1"]}, {"code": 657, "indent": 0, "parameters": ["Text = \"Turns Left \\\\v[119]\\nCombo \\\\v[139]\""]}, {"code": 657, "indent": 0, "parameters": ["Customized Settings = {\"Coordinates\":\"\",\"x:str\":\"1050\",\"y:s…"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 355, "indent": 0, "parameters": ["var images = ['water', 'earth', 'wind', 'earth', 'wind', 'water', 'earth', 'water', 'wind'];"]}, {"code": 655, "indent": 0, "parameters": ["var coords = [[64, 64], [256, 64], [448, 64], [64, 264], [256, 264], [448, 264], [64, 464], [256, 464], [448, 464]];"]}, {"code": 655, "indent": 0, "parameters": ["var elementVars = [180, 181, 182, 183, 184, 185, 186, 187, 188];"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Shuffle the images array for randomization"]}, {"code": 655, "indent": 0, "parameters": ["var shuffle = function(array) {"]}, {"code": 655, "indent": 0, "parameters": ["    var currentIndex = array.length, temporaryValue, randomIndex;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["    // While there remain elements to shuffle..."]}, {"code": 655, "indent": 0, "parameters": ["    while (0 !== currentIndex) {"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["        // Pick a remaining element..."]}, {"code": 655, "indent": 0, "parameters": ["        randomIndex = Math.floor(Math.random() * currentIndex);"]}, {"code": 655, "indent": 0, "parameters": ["        currentIndex -= 1;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["        // And swap it with the current element."]}, {"code": 655, "indent": 0, "parameters": ["        temporaryValue = array[currentIndex];"]}, {"code": 655, "indent": 0, "parameters": ["        array[currentIndex] = array[randomIndex];"]}, {"code": 655, "indent": 0, "parameters": ["        array[randomIndex] = temporaryValue;"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["    return array;"]}, {"code": 655, "indent": 0, "parameters": ["};"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["images = shuffle(images);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Display the images at the specified coordinates and set the variables"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < images.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    var pictureId = i + 1;"]}, {"code": 655, "indent": 0, "parameters": ["    var filename = 'card_' + images[i];"]}, {"code": 655, "indent": 0, "parameters": ["    var x = coords[i][0];"]}, {"code": 655, "indent": 0, "parameters": ["    var y = coords[i][1];"]}, {"code": 655, "indent": 0, "parameters": ["    $gameScreen.showPicture(pictureId, filename, 0, x, y, 20, 20, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["    // Assign values to variables based on the element type"]}, {"code": 655, "indent": 0, "parameters": ["    var elementValue = images[i] === 'water' ? 1 : (images[i] === 'earth' ? 2 : 3);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameVariables.setValue(elementVars[i], elementValue);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Initialize the base viewing time"]}, {"code": 655, "indent": 0, "parameters": ["var baseTime = 2000; // 2 seconds in milliseconds"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Get the current round number from the variable"]}, {"code": 655, "indent": 0, "parameters": ["var currentRound = $gameVariables.value(190);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Calculate the viewing time for the current round"]}, {"code": 655, "indent": 0, "parameters": ["// Decrease the viewing time by 200ms each round without setting a minimum limit"]}, {"code": 655, "indent": 0, "parameters": ["var viewTime = baseTime - (currentRound * 200);"]}, {"code": 655, "indent": 0, "parameters": ["viewTime = Math.max(0, viewTime); // Ensures time does not go below 0"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Display the cards with the calculated viewing time"]}, {"code": 655, "indent": 0, "parameters": ["setTimeout(function() {"]}, {"code": 655, "indent": 0, "parameters": ["    for (var i = 0; i < images.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["        var pictureId = i + 1;"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.showPicture(pictureId, 'card_back', 0, coords[i][0], coords[i][1], 20, 20, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}, viewTime);"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 355, "indent": 0, "parameters": ["// Define the cards and their scales"]}, {"code": 655, "indent": 0, "parameters": ["var cards = ['water', 'earth', 'wind'];"]}, {"code": 655, "indent": 0, "parameters": ["var targetScale = 40; // Percentage scale for the target card"]}, {"code": 655, "indent": 0, "parameters": ["var targetX = 900; // X coordinate for the target card"]}, {"code": 655, "indent": 0, "parameters": ["var targetY = 150; // Y coordinate for the target card"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Function to show the target card"]}, {"code": 655, "indent": 0, "parameters": ["function showTargetCard(card, scale, x, y) {"]}, {"code": 655, "indent": 0, "parameters": ["    // Use the RPG Maker MZ script call to show the picture"]}, {"code": 655, "indent": 0, "parameters": ["    // with the filename derived from the card, at the specified location and scale."]}, {"code": 655, "indent": 0, "parameters": ["    var pictureId = 10; // Use a free picture ID"]}, {"code": 655, "indent": 0, "parameters": ["    var filename = 'card_' + card;"]}, {"code": 655, "indent": 0, "parameters": ["    $gameScreen.showPicture(pictureId, filename, 0, x, y, scale, scale, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Function to save the card type to a variable"]}, {"code": 655, "indent": 0, "parameters": ["function saveCardType(card) {"]}, {"code": 655, "indent": 0, "parameters": ["    var cardTypeValue = cards.indexOf(card) + 1; // Convert card type to number (1 for water, 2 for earth, 3 for wind)"]}, {"code": 655, "indent": 0, "parameters": ["    $gameVariables.setValue(189, cardTypeValue); // Save the card type to variable 189"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Function to start rolling and select a random card"]}, {"code": 655, "indent": 0, "parameters": ["function startRolling(cards, targetX, targetY) {"]}, {"code": 655, "indent": 0, "parameters": ["    var rollSpeed = 50; // Time in milliseconds for each frame of the roll"]}, {"code": 655, "indent": 0, "parameters": ["    var rollCount = Math.floor(Math.random() * 20) + 10; // Random number of rolls between 10 and 30"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["    // Initial roll setup"]}, {"code": 655, "indent": 0, "parameters": ["    var currentRoll = 0;"]}, {"code": 655, "indent": 0, "parameters": ["    var intervalId = setInterval(function() {"]}, {"code": 655, "indent": 0, "parameters": ["        // Show the current card"]}, {"code": 655, "indent": 0, "parameters": ["        var currentCard = cards[currentRoll % cards.length];"]}, {"code": 655, "indent": 0, "parameters": ["        showTargetCard(currentCard, targetScale, targetX, targetY);"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["        currentRoll++;"]}, {"code": 655, "indent": 0, "parameters": ["        if (currentRoll >= rollCount) {"]}, {"code": 655, "indent": 0, "parameters": ["            clearInterval(intervalId); // Stop rolling"]}, {"code": 655, "indent": 0, "parameters": ["            // Randomly select the final card to show"]}, {"code": 655, "indent": 0, "parameters": ["            var finalCard = cards[Math.floor(Math.random() * cards.length)];"]}, {"code": 655, "indent": 0, "parameters": ["            showTargetCard(finalCard, targetScale, targetX, targetY);"]}, {"code": 655, "indent": 0, "parameters": ["            saveCardType(finalCard); // Save the final card type to the variable"]}, {"code": 655, "indent": 0, "parameters": ["        }"]}, {"code": 655, "indent": 0, "parameters": ["    }, rollSpeed);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["// Start the card rolling animation"]}, {"code": 655, "indent": 0, "parameters": ["startRolling(cards, targetX, targetY);"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_PictureChoices", "ChangePictureChoiceSettingsRange", "Picture Settings: Change Range", {"Step1": "", "StartID:num": "1", "EndingID:num": "9", "Step2": "", "OnSelectSettings:struct": "{\"Easing\":\"\",\"Duration:num\":\"10\",\"easingType:str\":\"Linear\",\"Position\":\"\",\"TargetX:str\":\"Unchanged\",\"TargetY:str\":\"Unchanged\",\"TargetScaleX:str\":\"20\",\"TargetScaleY:str\":\"20\",\"Blend\":\"\",\"TargetOpacity:str\":\"Unchanged\",\"BlendMode:num\":\"1\",\"Tone\":\"\",\"TargetToneRed:str\":\"Unchanged\",\"TargetToneGreen:str\":\"Unchanged\",\"TargetToneBlue:str\":\"Unchanged\",\"TargetToneGray:str\":\"Unchanged\"}", "OnDeselectSettings:struct": "{\"Easing\":\"\",\"Duration:num\":\"10\",\"easingType:str\":\"Linear\",\"Position\":\"\",\"TargetX:str\":\"Unchanged\",\"TargetY:str\":\"Unchanged\",\"TargetScaleX:str\":\"20\",\"TargetScaleY:str\":\"20\",\"Blend\":\"\",\"TargetOpacity:str\":\"Unchanged\",\"BlendMode:num\":\"0\",\"Tone\":\"\",\"TargetToneRed:str\":\"Unchanged\",\"TargetToneGreen:str\":\"Unchanged\",\"TargetToneBlue:str\":\"Unchanged\",\"TargetToneGray:str\":\"0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Step 1 = "]}, {"code": 657, "indent": 1, "parameters": ["Starting ID = 1"]}, {"code": 657, "indent": 1, "parameters": ["Ending ID = 9"]}, {"code": 657, "indent": 1, "parameters": ["Step 2 = "]}, {"code": 657, "indent": 1, "parameters": ["On Select Settings = {\"Easing\":\"\",\"Duration:num\":\"10\",\"easi…"]}, {"code": 657, "indent": 1, "parameters": ["On Deselect Settings = {\"Easing\":\"\",\"Duration:num\":\"10\",\"ea…"]}, {"code": 102, "indent": 1, "parameters": [["Card 1<Bind Picture: 1><Choice Common Event: 265><Hide Choice Window>", "Card 2<Bind Picture: 2><Choice Common Event: 265>", "Card 3<Bind Picture: 3><Choice Common Event: 265>", "Card 4<Bind Picture: 4><Choice Common Event: 265>", "Card 5<Bind Picture: 5><Choice Common Event: 265>", "Card 6<Bind Picture: 6><Choice Common Event: 265>"], -1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Card 1<Bind Picture: 1><Choice Common Event: 265><Hide Choice Window>"]}, {"code": 111, "indent": 2, "parameters": [1, 191, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 180, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [1, 0, 0, 0, 119, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [1, "card_water", 0, 0, 119, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [1, 0, 0, 0, 64, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 180, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [1, 0, 0, 0, 119, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [1, "card_earth", 0, 0, 119, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [1, 0, 0, 0, 64, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 180, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [1, 0, 0, 0, 119, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [1, "card_wind", 0, 0, 119, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [1, 0, 0, 0, 64, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 180, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [191, 191, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 0]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Card 2<Bind Picture: 2><Choice Common Event: 265>"]}, {"code": 111, "indent": 2, "parameters": [1, 192, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 181, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [2, 0, 0, 0, 309, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [2, "card_water", 0, 0, 309, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [2, 0, 0, 0, 256, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 181, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [2, 0, 0, 0, 309, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [2, "card_earth", 0, 0, 309, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [2, 0, 0, 0, 256, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 181, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [2, 0, 0, 0, 309, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [2, "card_wind", 0, 0, 309, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [2, 0, 0, 0, 256, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 181, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [192, 192, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 1]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Card 3<Bind Picture: 3><Choice Common Event: 265>"]}, {"code": 111, "indent": 2, "parameters": [1, 193, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 182, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [3, 0, 0, 0, 503, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [3, "card_water", 0, 0, 503, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [3, 0, 0, 0, 448, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 182, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [3, 0, 0, 0, 503, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [3, "card_earth", 0, 0, 503, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [3, 0, 0, 0, 448, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 182, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [3, 0, 0, 0, 503, 64, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [3, "card_wind", 0, 0, 503, 64, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [3, 0, 0, 0, 448, 64, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 182, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [193, 193, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 2]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Card 4<Bind Picture: 4><Choice Common Event: 265>"]}, {"code": 111, "indent": 2, "parameters": [1, 194, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 183, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [4, 0, 0, 0, 119, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [4, "card_water", 0, 0, 119, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [4, 0, 0, 0, 64, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 183, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [4, 0, 0, 0, 119, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [4, "card_earth", 0, 0, 119, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [4, 0, 0, 0, 64, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 183, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [4, 0, 0, 0, 119, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [4, "card_wind", 0, 0, 119, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [4, 0, 0, 0, 64, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 183, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [194, 194, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 3]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "Card 5<Bind Picture: 5><Choice Common Event: 265>"]}, {"code": 111, "indent": 2, "parameters": [1, 195, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 184, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [5, 0, 0, 0, 309, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [5, "card_water", 0, 0, 309, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [5, 0, 0, 0, 256, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 184, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [5, 0, 0, 0, 309, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [5, "card_earth", 0, 0, 309, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [5, 0, 0, 0, 256, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 184, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [5, 0, 0, 0, 309, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [5, "card_wind", 0, 0, 309, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [5, 0, 0, 0, 256, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 184, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [195, 195, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 4]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [5, "Card 6<Bind Picture: 6><Choice Common Event: 265>"]}, {"code": 111, "indent": 2, "parameters": [1, 196, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 185, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [6, 0, 0, 0, 503, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [6, "card_water", 0, 0, 503, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [6, 0, 0, 0, 448, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 185, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [6, 0, 0, 0, 503, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [6, "card_earth", 0, 0, 503, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [6, 0, 0, 0, 448, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 185, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [6, 0, 0, 0, 503, 264, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [6, "card_wind", 0, 0, 503, 264, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [6, 0, 0, 0, 448, 264, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 185, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [196, 196, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 5]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 102, "indent": 1, "parameters": [["Card 7<Bind Picture: 7><Choice Common Event: 265>", "Card 8<Bind Picture: 8><Choice Common Event: 265>", "Card 9<Bind Picture: 9><Choice Common Event: 265>"], -2, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Card 7<Bind Picture: 7><Choice Common Event: 265>"]}, {"code": 111, "indent": 2, "parameters": [1, 197, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 186, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [7, 0, 0, 0, 119, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [7, "card_water", 0, 0, 119, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [7, 0, 0, 0, 64, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 186, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [7, 0, 0, 0, 119, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [7, "card_earth", 0, 0, 119, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [7, 0, 0, 0, 64, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 186, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [7, 0, 0, 0, 119, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [7, "card_wind", 0, 0, 119, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [7, 0, 0, 0, 64, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 186, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [197, 197, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 6]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Card 8<Bind Picture: 8><Choice Common Event: 265>"]}, {"code": 111, "indent": 2, "parameters": [1, 198, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 187, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [8, 0, 0, 0, 309, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [8, "card_water", 0, 0, 309, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [8, 0, 0, 0, 256, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 187, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [8, 0, 0, 0, 309, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [8, "card_earth", 0, 0, 309, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [8, 0, 0, 0, 256, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 187, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [8, 0, 0, 0, 309, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [8, "card_wind", 0, 0, 309, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [8, 0, 0, 0, 256, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 187, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [198, 198, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 7]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Card 9<Bind Picture: 9><Choice Common Event: 265>"]}, {"code": 111, "indent": 2, "parameters": [1, 199, 0, 0, 0]}, {"code": 111, "indent": 3, "parameters": [1, 188, 0, 1, 0]}, {"code": 232, "indent": 4, "parameters": [9, 0, 0, 0, 503, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [9, "card_water", 0, 0, 503, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [9, 0, 0, 0, 448, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 188, 0, 2, 0]}, {"code": 232, "indent": 4, "parameters": [9, 0, 0, 0, 503, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [9, "card_earth", 0, 0, 503, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [9, 0, 0, 0, 448, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 188, 0, 3, 0]}, {"code": 232, "indent": 4, "parameters": [9, 0, 0, 0, 503, 464, 0, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 231, "indent": 4, "parameters": [9, "card_wind", 0, 0, 503, 464, 0, 20, 255, 0]}, {"code": 232, "indent": 4, "parameters": [9, 0, 0, 0, 448, 464, 20, 20, 255, 0, 20, false, 0]}, {"code": 230, "indent": 4, "parameters": [20]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 188, 1, 189, 0]}, {"code": 224, "indent": 4, "parameters": [[0, 255, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Chime2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [130, 130, 1, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 224, "indent": 4, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 250, "indent": 4, "parameters": [{"name": "Buzzer1", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 4, "parameters": [119, 119, 2, 0, 1]}, {"code": 122, "indent": 4, "parameters": [139, 139, 0, 0, 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 122, "indent": 3, "parameters": [199, 199, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 122, "indent": 2, "parameters": [59, 59, 0, 0, 8]}, {"code": 122, "indent": 2, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 403, "indent": 1, "parameters": [6, null]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 7/VisuMZ_3_VisualTextWindows", "RefreshVisualTextWindow", "Text Window: Refresh", {"list:arraynum": "[\"1\"]"}]}, {"code": 657, "indent": 1, "parameters": ["ID(s) = [\"1\"]"]}, {"code": 111, "indent": 1, "parameters": [1, 119, 0, 0, 2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 2, "parameters": ["You lose!"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 130, 0, 3, 1]}, {"code": 122, "indent": 2, "parameters": [190, 190, 1, 0, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 2, "parameters": ["You win!"]}, {"code": 111, "indent": 2, "parameters": [1, 190, 0, 10, 0]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["Start"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 235, "indent": 0, "parameters": [7]}, {"code": 235, "indent": 0, "parameters": [8]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 201, "indent": 0, "parameters": [0, 168, 17, 22, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "mondrus-walking2[VS8]", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [180, 180, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [181, 181, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [182, 182, 0, 0, 0]}, {"code": 108, "indent": 0, "parameters": ["Card NW"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 111, "indent": 1, "parameters": [1, 180, 0, 3, 4]}, {"code": 231, "indent": 2, "parameters": [1, "card_water", 0, 0, 64, 64, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 111, "indent": 2, "parameters": [1, 181, 0, 3, 4]}, {"code": 231, "indent": 3, "parameters": [1, "card_earth", 0, 0, 64, 64, 20, 20, 255, 0]}, {"code": 122, "indent": 3, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 111, "indent": 3, "parameters": [1, 182, 0, 3, 4]}, {"code": 231, "indent": 4, "parameters": [1, "card_wind", 0, 0, 64, 64, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["Card N"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 111, "indent": 1, "parameters": [1, 180, 0, 3, 4]}, {"code": 231, "indent": 2, "parameters": [2, "card_water", 0, 0, 256, 72, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 181, 0, 3, 4]}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 1, 0]}, {"code": 231, "indent": 4, "parameters": [2, "card_earth", 0, 0, 256, 72, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 182, 0, 3, 4]}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 2, 0]}, {"code": 231, "indent": 5, "parameters": [2, "card_wind", 0, 0, 256, 72, 20, 20, 255, 0]}, {"code": 122, "indent": 5, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["Card NE"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 180, 0, 3, 4]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 231, "indent": 2, "parameters": [3, "card_water", 0, 0, 448, 72, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 181, 0, 3, 4]}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [3, "card_earth", 0, 0, 448, 72, 20, 20, 255, 0]}, {"code": 122, "indent": 3, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 182, 0, 3, 4]}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 2, 0]}, {"code": 231, "indent": 4, "parameters": [3, "card_wind", 0, 0, 448, 72, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["Card W"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 180, 0, 3, 4]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 231, "indent": 2, "parameters": [4, "card_water", 0, 0, 64, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 181, 0, 3, 4]}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [4, "card_earth", 0, 0, 64, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 3, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 182, 0, 3, 4]}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 2, 0]}, {"code": 231, "indent": 4, "parameters": [4, "card_wind", 0, 0, 64, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["Card M"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 180, 0, 3, 4]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 231, "indent": 2, "parameters": [5, "card_water", 0, 0, 256, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 181, 0, 3, 4]}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [5, "card_earth", 0, 0, 256, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 3, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 182, 0, 3, 4]}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 2, 0]}, {"code": 231, "indent": 4, "parameters": [5, "card_wind", 0, 0, 256, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["Card E"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 180, 0, 3, 4]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 231, "indent": 2, "parameters": [6, "card_water", 0, 0, 448, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 181, 0, 3, 4]}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [6, "card_earth", 0, 0, 448, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 3, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 182, 0, 3, 4]}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 2, 0]}, {"code": 231, "indent": 4, "parameters": [6, "card_wind", 0, 0, 448, 264, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["Card SW"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 180, 0, 3, 4]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 231, "indent": 2, "parameters": [7, "card_water", 0, 0, 64, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 181, 0, 3, 4]}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [7, "card_earth", 0, 0, 64, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 3, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 182, 0, 3, 4]}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 2, 0]}, {"code": 231, "indent": 4, "parameters": [7, "card_wind", 0, 0, 64, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["Card S"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 180, 0, 3, 4]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 231, "indent": 2, "parameters": [8, "card_water", 0, 0, 256, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 181, 0, 3, 4]}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [8, "card_earth", 0, 0, 256, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 3, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 182, 0, 3, 4]}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 2, 0]}, {"code": 231, "indent": 4, "parameters": [8, "card_wind", 0, 0, 256, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["Card SE"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 180, 0, 3, 4]}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 0, 0]}, {"code": 231, "indent": 2, "parameters": [9, "card_water", 0, 0, 448, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 2, "parameters": [180, 180, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 181, 0, 3, 4]}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [9, "card_earth", 0, 0, 448, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 3, "parameters": [181, 181, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 182, 0, 3, 4]}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 2, 0]}, {"code": 231, "indent": 4, "parameters": [9, "card_wind", 0, 0, 448, 456, 20, 20, 255, 0]}, {"code": 122, "indent": 4, "parameters": [182, 182, 1, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 6}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "aiya-walking2[VS8]", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["An archery contest? Too bad I've never touched"]}, {"code": 401, "indent": 0, "parameters": ["a bow before in my life."]}, {"code": 101, "indent": 0, "parameters": ["zayne-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What about <PERSON><PERSON>? I've heard elven accuracy"]}, {"code": 401, "indent": 0, "parameters": ["with a bow and arrow is unmatched!"]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["I prefer my dagger. Bows and quivers are"]}, {"code": 401, "indent": 0, "parameters": ["cumbersome, and it's easy to lose your arrows."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["But that doesn't mean I don't know my way"]}, {"code": 401, "indent": 0, "parameters": ["around an archery range. Our tutor insisted"]}, {"code": 401, "indent": 0, "parameters": ["that <PERSON><PERSON><PERSON> and I go and practice our aim at"]}, {"code": 401, "indent": 0, "parameters": ["least once a month, for hunting she said."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["I never liked killing innocent animals, but I"]}, {"code": 401, "indent": 0, "parameters": ["was always a better shot than my brother. He"]}, {"code": 401, "indent": 0, "parameters": ["got mad when I would split his arrows."]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["So does that mean you'll do it?"]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["I've never wasted a good opportunity to"]}, {"code": 401, "indent": 0, "parameters": ["sharpen off my skills. Sign me up!"]}, {"code": 201, "indent": 0, "parameters": [0, 72, 13, 14, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 6}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "aigi-walking", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["What can I do for you?"]}, {"code": 102, "indent": 1, "parameters": [["Socket Gems", "Combine Gems", "Trade Junk", "Cancel"], 3, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Socket Gems"]}, {"code": 357, "indent": 2, "parameters": ["DhoomEquipmentSockets", "OpenEquipSocketMenu", "Open Equipment Socket Menu", {}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Combine Gems"]}, {"code": 357, "indent": 2, "parameters": ["Wave 4/VisuMZ_2_ItemCraftingSys", "ItemCraftingSceneOpen", "Scene: <PERSON><PERSON> (All)", {}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Trade Junk"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "Cancel"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 6}, {"id": 9, "name": "EV009", "note": "<shadow><Breathing Rate: 1%>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Blacksmith2[VS8]", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 45, "parameters": ["Force Carry: Off"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["Force Carry: Off"], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["I can make some unique offhands for you, but it will cost"]}, {"code": 401, "indent": 0, "parameters": ["a little extra."]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["What can I do for you?"]}, {"code": 102, "indent": 1, "parameters": [["Socket Gems", "Combine Gems", "Secret Stash", "No Thanks!"], 3, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "Socket Gems"]}, {"code": 357, "indent": 2, "parameters": ["DhoomEquipmentSockets", "OpenEquipSocketMenu", "Open Equipment Socket Menu", {}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "Combine Gems"]}, {"code": 357, "indent": 2, "parameters": ["Wave 4/VisuMZ_2_ItemCraftingSys", "ItemCraftingSceneOpen", "Scene: <PERSON><PERSON> (All)", {}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "Secret Stash"]}, {"code": 302, "indent": 2, "parameters": [2, 130, 0, 0, true]}, {"code": 605, "indent": 2, "parameters": [2, 131, 0, 0]}, {"code": 605, "indent": 2, "parameters": [2, 150, 0, 0]}, {"code": 605, "indent": 2, "parameters": [2, 151, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "No Thanks!"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Check in at the counter for our standard equipment."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 45, "parameters": ["Force Carry: On"], "indent": 0}, {"code": 17, "indent": null}, {"code": 45, "parameters": ["Force Carry: On"], "indent": null}, {"code": 17, "indent": 0}, {"code": 45, "parameters": ["Animation: 290"], "indent": null}, {"code": 44, "parameters": [{"name": "Bell2", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 45, "parameters": ["Force Carry: On"], "indent": 0}, {"code": 17, "indent": 0}, {"code": 15, "parameters": [100], "indent": null}, {"code": 45, "parameters": ["Force Carry: On"], "indent": 0}, {"code": 17, "indent": 0}, {"code": 45, "parameters": ["Animation: 290"], "indent": 0}, {"code": 44, "parameters": [{"name": "Bell2", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 45, "parameters": ["Force Carry: On"], "indent": 0}, {"code": 17, "indent": 0}, {"code": 15, "parameters": [200], "indent": null}, {"code": 45, "parameters": ["Force Carry: Off"], "indent": null}, {"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 45, "parameters": ["setSelfSwitchValue(18, 4, 'A', true)"], "indent": null}, {"code": 44, "parameters": [{"name": "Water1", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [240], "indent": null}, {"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 45, "parameters": ["setSelfSwitchValue(18, 5, 'A', true)"], "indent": null}, {"code": 44, "parameters": [{"name": "Sword1", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [120], "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 8}, {"id": 10, "name": "EV010", "note": "<shadow><Breathing Rate: 1%>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Yellow Shopkeeper1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Weapon Merchant"]}, {"code": 401, "indent": 0, "parameters": ["Take a look at our weapons and offhands, all"]}, {"code": 401, "indent": 0, "parameters": ["made right here in shop!"]}, {"code": 302, "indent": 0, "parameters": [1, 3, 0, 0, false]}, {"code": 605, "indent": 0, "parameters": [1, 4, 0, 0]}, {"code": 605, "indent": 0, "parameters": [1, 22, 0, 0]}, {"code": 605, "indent": 0, "parameters": [1, 23, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 122, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 123, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 142, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 143, 0, 0]}, {"code": 605, "indent": 0, "parameters": [1, 119, 0, 0]}, {"code": 605, "indent": 0, "parameters": [1, 103, 0, 0]}, {"code": 605, "indent": 0, "parameters": [1, 57, 0, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Weapon Merchant"]}, {"code": 401, "indent": 0, "parameters": ["Come back some time!"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 10, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 8}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "8D ACTOR3-5[VS8]", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["Rosedale_SkillTree", "start_skill_tree", "Start Skill Scene", {"actor": "1"}]}, {"code": 657, "indent": 0, "parameters": ["Actor = 1"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 6}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Desert Bath", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Which game?"]}, {"code": 102, "indent": 0, "parameters": [["Cannon Defense", "Ball Cascade", "Sky Pirates", "Match 3"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Cannon Defense"]}, {"code": 357, "indent": 1, "parameters": ["CatapultDefense2", "StartCatapultDefense", "Start Minigame", {}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Ball Cascade"]}, {"code": 357, "indent": 1, "parameters": ["Peglin", "StartPeglin", "Start Peglin Game", {}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "Sky Pirates"]}, {"code": 357, "indent": 1, "parameters": ["SkyPirates", "StartSkyPirates", "Start Sky Pirates", {}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "Match 3"]}, {"code": 357, "indent": 1, "parameters": ["Match3", "startMatch3", "Start Match-3 Game", {}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 6}, {"id": 13, "name": "EV013", "note": "<shadow><Breathing Rate: 1%>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Yellow Shopkeeper2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Armor Merchant"]}, {"code": 401, "indent": 0, "parameters": ["Take a look at our armor, all made"]}, {"code": 401, "indent": 0, "parameters": ["right here in shop!"]}, {"code": 302, "indent": 0, "parameters": [2, 22, 0, 0, false]}, {"code": 605, "indent": 0, "parameters": [2, 23, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 42, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 43, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 62, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 63, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 82, 0, 0]}, {"code": 605, "indent": 0, "parameters": [2, 83, 0, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Armor Merchant"]}, {"code": 401, "indent": 0, "parameters": ["Come back some time!"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 10, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 8}, null]}