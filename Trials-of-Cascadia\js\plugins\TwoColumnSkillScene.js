/*:
 * @target MZ
 * @plugindesc Modify the skill, augment, and passive scenes to display items in two columns with taller item bars.
 * @help This plugin modifies the skill list, augment list, and passive list windows to display two columns with taller item heights, while keeping icons and other information intact.
 *
 */

(() => {
    // Modify the Skill Scene
    const _Scene_Skill_createItemWindow = Scene_Skill.prototype.createItemWindow;
    Scene_Skill.prototype.createItemWindow = function () {
        _Scene_Skill_createItemWindow.call(this);

        // Adjust the skill list window to use two columns
        this._itemWindow.maxCols = function () {
            return 2; // Set the number of columns to 2
        };

        // Set a custom height for the items to make them visually taller
        this._itemWindow.itemHeight = function () {
            return 88; // Set the item height to a larger value (default is usually around 36)
        };
    };
})();
