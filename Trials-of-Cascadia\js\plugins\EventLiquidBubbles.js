//=============================================================================
// EventLiquidBubbles.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc (v1.0) Adds liquid bubble effects to events to simulate bubbling liquids in containers.
 * @url https://yourwebsite.com
 *
 * @param bubbleColor
 * @text Bubble Color
 * @desc The color of the bubbles (hex color code).
 * @default #4080FF
 * @type string
 *
 * @param bubbleSize
 * @text Bubble Size
 * @desc The size of individual bubbles.
 * @default 2
 * @type number
 * @min 1
 * @max 8
 *
 * @param bubbleCount
 * @text Bubble Count
 * @desc The number of bubbles to generate.
 * @default 6
 * @type number
 * @min 1
 * @max 15
 *
 * @param bubbleSpeed
 * @text Bubble Speed
 * @desc The speed of bubble movement upward.
 * @default 1.5
 * @type number
 * @min 0.5
 * @max 3
 * @decimals 1
 *
 * @param bubbleLifetime
 * @text Bubble Lifetime
 * @desc How long bubbles last before disappearing (in frames).
 * @default 120
 * @type number
 * @min 60
 * @max 240
 *
 * @param bubbleWobble
 * @text Bubble Wobble
 * @desc Whether bubbles should wobble as they rise.
 * @default true
 * @type boolean
 *
 * @param bubbleYOffset
 * @text Bubble Y Offset
 * @desc Vertical offset for bubble spawn position (positive = below event, negative = above event).
 * @default 0.3
 * @type number
 * @min -1
 * @max 2
 * @decimals 1
 *
 * @param bubbleXSpread
 * @text Bubble X Spread
 * @desc How wide bubbles spread horizontally as they rise (in pixels).
 * @default 20
 * @type number
 * @min 5
 * @max 100
 *
 * @param bubbleHueShift
 * @text Bubble Hue Shift
 * @desc Random hue variation for bubbles (0 = no variation, 30 = significant color variation).
 * @default 10
 * @type number
 * @min 0
 * @max 60
 *
 * @help
 * ============================================================================
 * Event Liquid Bubbles Plugin
 * ============================================================================
 * 
 * This plugin adds a notetag system to make events appear to have liquid
 * bubbles bubbling upwards. Perfect for creating atmosphere in sci-fi,
 * industrial, or fantasy settings with liquid containers.
 * 
 * ============================================================================
 * NOTETAGS
 * ============================================================================
 * 
 * Add this notetag to any event to enable liquid bubbles:
 * <bubbles>
 * 
 * You can also customize the bubbling for individual events:
 * <bubbles:color=#00FF00,size=6,count=8,speed=2.0>
 * 
 * Available parameters:
 * - color: Hex color code (e.g., #00FF00 for green)
 * - size: Bubble size (1-8)
 * - count: Number of bubbles (1-15)
 * - speed: Movement speed (0.5-3.0)
 * - lifetime: Duration in frames (60-240)
 * - wobble: true/false
 * - yOffset: Vertical spawn offset (-1 to 2)
 * - xSpread: Horizontal spread as bubbles rise (5-100 pixels)
 * - hueShift: Random hue variation (0-60 degrees)
 * 
 * ============================================================================
 * EXAMPLES
 * ============================================================================
 * 
 * Basic bubbling:
 * <bubbles>
 * 
 * Green bubbles with custom settings:
 * <bubbles:color=#00FF00,size=3,count=8,speed=2.0>
 * 
 * Blue wobbling bubbles with custom offset, spread, and hue variation:
 * <bubbles:color=#0088FF,size=2,count=10,wobble=true,yOffset=0.5,xSpread=30,hueShift=15>
 * 
 * ============================================================================
 * TERMS OF USE
 * ============================================================================
 * 
 * This plugin is free for both commercial and non-commercial use.
 * Attribution is appreciated but not required.
 * 
 * @license MIT
 */

(() => {
    'use strict';

    // Plugin parameters
    const pluginName = "EventLiquidBubbles";
    const parameters = PluginManager.parameters(pluginName);
    
    const BUBBLE_COLOR = parameters['bubbleColor'] || '#4080FF';
    const BUBBLE_SIZE = Number(parameters['bubbleSize']) || 2;
    const BUBBLE_COUNT = Number(parameters['bubbleCount']) || 6;
    const BUBBLE_SPEED = Number(parameters['bubbleSpeed']) || 1.5;
    const BUBBLE_LIFETIME = Number(parameters['bubbleLifetime']) || 120;
    const BUBBLE_WOBBLE = parameters['bubbleWobble'] === 'true';
    const BUBBLE_Y_OFFSET = Number(parameters['bubbleYOffset']) || 0.3;
    const BUBBLE_X_SPREAD = Number(parameters['bubbleXSpread']) || 20;
    const BUBBLE_HUE_SHIFT = Number(parameters['bubbleHueShift']) || 10;

    //=============================================================================
    // Color Utility Functions
    //=============================================================================

    // Convert hex to HSL, apply hue shift, then back to hex
    function shiftHue(hexColor, hueShift) {
        if (!hueShift || hueShift === 0) return hexColor;
        
        // Convert hex to RGB
        const hex = hexColor.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16) / 255;
        const g = parseInt(hex.substr(2, 2), 16) / 255;
        const b = parseInt(hex.substr(4, 2), 16) / 255;
        
        // Convert RGB to HSL
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;
        
        if (max === min) {
            h = s = 0; // achromatic
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }
        
        // Apply hue shift
        h = (h + hueShift / 360) % 1;
        if (h < 0) h += 1;
        
        // Convert HSL back to RGB
        function hue2rgb(p, q, t) {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        }
        
        let newR, newG, newB;
        if (s === 0) {
            newR = newG = newB = l; // achromatic
        } else {
            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;
            newR = hue2rgb(p, q, h + 1/3);
            newG = hue2rgb(p, q, h);
            newB = hue2rgb(p, q, h - 1/3);
        }
        
        // Convert back to hex
        const toHex = (c) => {
            const hex = Math.round(c * 255).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };
        
        return '#' + toHex(newR) + toHex(newG) + toHex(newB);
    }

    //=============================================================================
    // Object Pooling System
    //=============================================================================

    class ObjectPool {
        constructor(createFn, resetFn, maxSize = 50) {
            this.createFn = createFn;
            this.resetFn = resetFn;
            this.maxSize = maxSize;
            this.pool = [];
            this.active = [];
        }

        get() {
            let obj;
            if (this.pool.length > 0) {
                obj = this.pool.pop();
            } else {
                obj = this.createFn();
            }
            this.active.push(obj);
            return obj;
        }

        release(obj) {
            const index = this.active.indexOf(obj);
            if (index !== -1) {
                this.active.splice(index, 1);
                if (this.pool.length < this.maxSize) {
                    this.resetFn(obj);
                    this.pool.push(obj);
                } else {
                    obj.destroy();
                }
            }
        }

        clear() {
            this.active.forEach(obj => obj.destroy());
            this.pool.forEach(obj => obj.destroy());
            this.active = [];
            this.pool = [];
        }
    }

    //=============================================================================
    // Liquid Bubble Class
    //=============================================================================

    class LiquidBubble extends Sprite {
        constructor(x, y, settings) {
            super();
            this.x = x;
            this.y = y;

            // Bubble physics - upward movement with horizontal spread
            this.vx = (Math.random() - 0.5) * (settings.xSpread || BUBBLE_X_SPREAD) * 0.01; // Horizontal spread
            this.vy = -settings.speed * (0.8 + Math.random() * 0.4); // Upward movement

            this.life = settings.lifetime * (0.7 + Math.random() * 0.6); // Variable lifetime
            this.maxLife = this.life;
            this.size = settings.size * (0.6 + Math.random() * 0.8); // Variable size
            this.color = settings.color;
            this.hueShift = settings.hueShift || BUBBLE_HUE_SHIFT;
            this.wobble = settings.wobble;
            this.visible = true;
            this._destroyed = false;

            // Bubble properties for realistic movement
            this.gravity = 0; // No gravity - bubbles only go up
            this.airResistance = 0.998; // Very minimal air resistance
            this.wobbleAngle = Math.random() * Math.PI * 2; // Random wobble start
            this.wobbleSpeed = 0.1 + Math.random() * 0.2; // Wobble speed
            this.wobbleAmplitude = 1 + Math.random() * 2; // Wobble amount

            // Create bitmap for the bubble using canvas
            this.createBubbleBitmap();
            this.anchor.set(0.5, 0.5);
            this.blendMode = PIXI.BLEND_MODES.NORMAL;
        }

        createBubbleBitmap() {
            const size = Math.max(16, this.size * 3); // Larger canvas for bubble details
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Apply random hue shift to the bubble color
            const shiftedColor = shiftHue(this.color, (Math.random() - 0.5) * this.hueShift);
            
            // Convert hex color to RGB for gradient
            const hexColor = shiftedColor.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Create bubble with highlight effect
            const bubbleRadius = this.size * 1.2;
            const highlightRadius = this.size * 0.3;
            const highlightOffset = this.size * 0.4;

            // Main bubble (semi-transparent with border)
            ctx.beginPath();
            ctx.arc(center, center, bubbleRadius, 0, Math.PI * 2);
            
            // Create radial gradient for bubble
            const gradient = ctx.createRadialGradient(center, center, 0, center, center, bubbleRadius);
            gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, 0.3)`); // Transparent center
            gradient.addColorStop(0.3, `rgba(${r}, ${g}, ${b}, 0.4)`); // Slightly more opaque
            gradient.addColorStop(0.7, `rgba(${r}, ${g}, ${b}, 0.6)`); // More opaque
            gradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, 0.8)`); // Opaque edge
            
            ctx.fillStyle = gradient;
            ctx.fill();

            // Add bubble border
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.9)`;
            ctx.lineWidth = 1;
            ctx.stroke();

            // Add highlight (white circle)
            const highlightX = center - highlightOffset;
            const highlightY = center - highlightOffset;
            ctx.beginPath();
            ctx.arc(highlightX, highlightY, highlightRadius, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.fill();

            // Add secondary highlight (smaller, more transparent)
            const secondaryHighlightRadius = this.size * 0.15;
            const secondaryHighlightOffset = this.size * 0.2;
            ctx.beginPath();
            ctx.arc(highlightX + secondaryHighlightOffset, highlightY + secondaryHighlightOffset, secondaryHighlightRadius, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
            ctx.fill();

            this.bitmap = bitmap;
        }

        update() {
            if (this._destroyed) return false;

            // Store previous position for potential trail effects
            this.previousX = this.x;
            this.previousY = this.y;

            // Bubble physics - upward movement with natural behavior
            this.x += this.vx;
            this.y += this.vy;
            this.life--;

            // Apply air resistance only (no gravity)
            this.vx *= this.airResistance;
            this.vy *= this.airResistance;

            // Wobble effect for realistic bubble movement
            if (this.wobble) {
                this.wobbleAngle += this.wobbleSpeed;
                const wobbleX = Math.sin(this.wobbleAngle) * this.wobbleAmplitude;
                const wobbleY = Math.cos(this.wobbleAngle * 0.7) * this.wobbleAmplitude * 0.5;
                this.x += wobbleX * 0.1;
                this.y += wobbleY * 0.1;
            }

            // Slight random drift changes for natural movement
            if (Math.random() < 0.02) {
                this.vx += (Math.random() - 0.5) * 0.2;
                this.vy += (Math.random() - 0.5) * 0.1;
            }

            // Update opacity with gradual fade
            const lifeFactor = this.life / this.maxLife;
            const alpha = Math.pow(lifeFactor, 0.8); // Gradual fade
            this.opacity = Math.max(0.1, alpha * 255);

            // Slight size variation as bubble rises
            const sizeVariation = 0.9 + (lifeFactor * 0.2);
            this.scale.set(sizeVariation, sizeVariation);

            if (this.life <= 0 || this.opacity < 10) {
                this._destroyed = true;
                return false;
            }

            return true;
        }

        destroy(options) {
            this._destroyed = true;
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
            try {
                super.destroy(options);
            } catch (e) {
                // Ignore destroy errors
            }
        }
    }



    //=============================================================================
    // Event Bubble Manager (attached to Scene_Map)
    //=============================================================================

    class EventBubbleManager {
        constructor(scene) {
            this.scene = scene;
            this.bubblingEvents = new Map(); // eventId -> bubble data
            this.bubbleTimer = 0;
            this.bubbleInterval = 20; // Base interval for bubbles
            this.nextBubbleDelay = this.getRandomizedInterval(this.bubbleInterval);

            // Object pools for performance
            this.bubblePool = new ObjectPool(
                () => new LiquidBubble(0, 0, getDefaultSettings()),
                (bubble) => this.resetBubble(bubble),
                25
            );

            // Active sprites (for updates)
            this.activeBubbleSprites = [];

            // Create bubble layer as child of spriteset for automatic zoom compatibility
            this.bubbleLayer = new Sprite();
            this.bubbleLayer.z = 4; // Above characters but below UI

            // Add to spriteset instead of scene for automatic zoom scaling
            if (scene._spriteset) {
                scene._spriteset.addChild(this.bubbleLayer);
            } else {
                scene.addChild(this.bubbleLayer);
            }

            console.log('EventLiquidBubbles: EventBubbleManager created with object pooling');
        }

        addBubblingEvent(eventId, settings) {
            this.bubblingEvents.set(eventId, {
                settings: settings,
                lastBubbleTime: 0
            });
            console.log(`EventLiquidBubbles: Added bubbling event ${eventId}:`, settings);
        }

        removeBubblingEvent(eventId) {
            this.bubblingEvents.delete(eventId);
        }

        resetBubble(bubble) {
            if (!bubble) return;

            bubble.visible = true;
            bubble._destroyed = false;
            bubble.life = 0;
            bubble.maxLife = 0;
            bubble.opacity = 255;
            bubble.scale.set(1, 1);
            bubble.eventId = null;
            bubble.initialX = 0;
            bubble.initialY = 0;
        }



        update() {
            // Update bubble generation with randomized breaks
            this.bubbleTimer++;
            if (this.bubbleTimer >= this.nextBubbleDelay) {
                this.bubbleTimer = 0;
                this.updateBubbles();
                // Set next randomized delay
                this.nextBubbleDelay = this.getRandomizedInterval(this.bubbleInterval);
            }

            // Update bubble layer position to match spriteset zoom
            this.updateBubbleLayerPosition();

            // Update existing bubble sprites using object pooling
            this.activeBubbleSprites = this.activeBubbleSprites.filter(bubble => {
                try {
                    if (!bubble) return false;
                    
                    // Update bubble position to follow event
                    this.updateBubblePosition(bubble);
                    
                    const alive = bubble.update();
                    if (!alive) {
                        if (this.bubbleLayer && bubble.parent) {
                            this.bubbleLayer.removeChild(bubble);
                        }
                        this.bubblePool.release(bubble);
                    }
                    return alive;
                } catch (e) {
                    console.warn('EventLiquidBubbles: Bubble update error:', e);
                    return false;
                }
            });


        }

        updateBubbleLayerPosition() {
            // Match the spriteset's position and scale for zoom compatibility
            const spriteset = this.scene._spriteset;
            if (!spriteset) return;

            // The bubble layer should follow the spriteset's position offset
            this.bubbleLayer.x = 0;
            this.bubbleLayer.y = 0;
        }

        updateBubblePosition(bubble) {
            if (!bubble.eventId) return;
            
            const event = $gameMap.event(bubble.eventId);
            if (!event) return;
            
            const sprite = this.scene._spriteset.findTargetSprite(event);
            if (!sprite) return;
            
            // Get the event's current position
            const eventX = sprite.x;
            const eventY = sprite.y + sprite.height * this.bubblingEvents.get(bubble.eventId).settings.yOffset;
            
            // Calculate the bubble's offset from the event's spawn point
            const offsetX = bubble.x - bubble.initialX;
            const offsetY = bubble.y - bubble.initialY;
            
            // Update bubble position to follow event
            bubble.x = eventX + offsetX;
            bubble.y = eventY + offsetY;
            
            // Update initial position for next frame
            bubble.initialX = eventX;
            bubble.initialY = eventY;
        }



        getRandomizedInterval(baseInterval) {
            // Create randomized breaks for natural bubbling
            const rand = Math.random();
            if (rand < 0.2) {
                // 20% chance of longer break (1.5-2.5x longer)
                return baseInterval * (1.5 + Math.random() * 1);
            } else if (rand < 0.5) {
                // 30% chance of medium break (1.2-1.5x longer)
                return baseInterval * (1.2 + Math.random() * 0.3);
            } else {
                // 50% chance of normal timing with small variation
                return baseInterval * (0.8 + Math.random() * 0.4);
            }
        }

        updateBubbles() {
            // Generate bubbles for each bubbling event
            this.bubblingEvents.forEach((data, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) return;

                // Get event sprite position (relative to spriteset)
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) return;

                // Use sprite's local position since bubble layer is child of spriteset
                const screenX = sprite.x;
                const screenY = sprite.y + sprite.height * data.settings.yOffset; // Use custom Y offset

                // Generate bubbles
                this.generateBubblesAt(screenX, screenY, data.settings, eventId);
            });
        }



        generateBubblesAt(x, y, settings, eventId) {
            // Limit total bubbles for performance
            if (this.activeBubbleSprites.length >= 15) return;

            // Natural bubbling behavior with varying intensity
            const bubbleChance = Math.random();
            let bubblesToGenerate = 0;

            if (bubbleChance < 0.4) {
                bubblesToGenerate = 0; // No bubbles 40% of the time
            } else if (bubbleChance < 0.7) {
                bubblesToGenerate = 1; // Single bubble 30% of the time
            } else if (bubbleChance < 0.9) {
                bubblesToGenerate = 2; // Two bubbles 20% of the time
            } else {
                bubblesToGenerate = Math.min(settings.count, 3); // Burst 10% of the time
            }

            for (let i = 0; i < bubblesToGenerate; i++) {
                // Spawn area at the bottom of the container
                const baseSpread = 20; // Spread across container width
                const offsetX = (Math.random() - 0.5) * baseSpread;
                const offsetY = Math.random() * 10; // Start from bottom

                try {
                    // Get bubble from pool and initialize
                    const bubble = this.bubblePool.get();
                    if (!bubble) continue;

                    bubble.x = x + offsetX;
                    bubble.y = y + offsetY;

                    // Store event ID for tracking
                    bubble.eventId = eventId;
                    bubble.initialX = x + offsetX;
                    bubble.initialY = y + offsetY;

                    // Reinitialize bubble properties
                    const speed = settings.speed * (0.8 + Math.random() * 0.4);
                    bubble.vx = (Math.random() - 0.5) * (settings.xSpread || BUBBLE_X_SPREAD) * 0.01;
                    bubble.vy = -speed;
                    bubble.life = settings.lifetime * (0.7 + Math.random() * 0.6);
                    bubble.maxLife = bubble.life;
                    bubble.size = settings.size * (0.6 + Math.random() * 0.8);
                    bubble.color = settings.color;
                    bubble.hueShift = settings.hueShift || BUBBLE_HUE_SHIFT;
                    bubble.wobble = settings.wobble;
                    bubble.wobbleAngle = Math.random() * Math.PI * 2;
                    bubble.wobbleSpeed = 0.1 + Math.random() * 0.2;
                    bubble.wobbleAmplitude = 1 + Math.random() * 2;

                    // Recreate bubble bitmap with new properties
                    bubble.createBubbleBitmap();

                    this.bubbleLayer.addChild(bubble);
                    this.activeBubbleSprites.push(bubble);
                } catch (e) {
                    console.warn('EventLiquidBubbles: Bubble creation error:', e);
                }
            }
        }



        destroy() {
            // Clean up object pools
            this.bubblePool.clear();
            this.activeBubbleSprites = [];

            if (this.bubbleLayer && this.bubbleLayer.parent) {
                this.bubbleLayer.parent.removeChild(this.bubbleLayer);
            }
            this.bubbleLayer.destroy();

            console.log('EventLiquidBubbles: EventBubbleManager destroyed with object pools cleaned');
        }
    }

    // Utility functions
    function parseBubbleSettings(eventData) {
        if (!eventData || !eventData.note) return null;

        const note = eventData.note;

        // Check for both <bubbles> and <bubbles:...> tags
        if (!note.includes('<bubbles>') && !note.includes('<bubbles:')) {
            return null;
        }

        console.log('EventLiquidBubbles: Bubble tag found! Creating bubbling effect');
        const settings = getDefaultSettings();

        // Parse custom parameters if they exist
        const match = note.match(/<bubbles:(.*?)>/);
        if (match) {
            const params = match[1].split(',');
            params.forEach(param => {
                const [key, value] = param.split('=');
                if (key && value) {
                    switch (key.trim()) {
                        case 'color':
                            settings.color = value.trim();
                            break;
                        case 'size':
                            settings.size = Number(value.trim());
                            break;
                        case 'count':
                            settings.count = Number(value.trim());
                            break;
                        case 'speed':
                            settings.speed = Number(value.trim());
                            break;
                        case 'lifetime':
                            settings.lifetime = Number(value.trim());
                            break;
                        case 'wobble':
                            settings.wobble = value.trim() === 'true';
                            break;
                        case 'yOffset':
                            settings.yOffset = Number(value.trim());
                            break;
                        case 'xSpread':
                            settings.xSpread = Number(value.trim());
                            break;
                        case 'hueShift':
                            settings.hueShift = Number(value.trim());
                            break;
                    }
                }
            });
        }

        return settings;
    }

    function getDefaultSettings() {
        return {
            color: BUBBLE_COLOR,
            size: BUBBLE_SIZE,
            count: BUBBLE_COUNT,
            speed: BUBBLE_SPEED,
            lifetime: BUBBLE_LIFETIME,
            wobble: BUBBLE_WOBBLE,
            yOffset: BUBBLE_Y_OFFSET,
            xSpread: BUBBLE_X_SPREAD,
            hueShift: BUBBLE_HUE_SHIFT
        };
    }

    //=============================================================================
    // Plugin Commands
    //=============================================================================

    if (typeof PluginManager !== 'undefined') {
        PluginManager.registerCommand(pluginName, "ToggleBubbling", args => {
            const eventId = Number(args.eventId);
            const enabled = args.enabled === 'true';

            const event = $gameMap.event(eventId);
            if (event) {
                if (enabled) {
                    event.setMeta('bubbles', 'true');
                } else {
                    event.setMeta('bubbles', 'false');
                }
                $gameMap.requestRefresh();
            }
        });
    }

    //=============================================================================
    // Scene_Map Integration
    //=============================================================================

    const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
    Scene_Map.prototype.createSpriteset = function() {
        _Scene_Map_createSpriteset.call(this);

        // Create bubble manager after spriteset is created
        this._bubbleManager = new EventBubbleManager(this);

        console.log('EventLiquidBubbles: Scene_Map spriteset created with bubble manager');
    };

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);

        // Update bubble manager
        if (this._bubbleManager) {
            this._bubbleManager.update();
        }
    };

    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        // Clean up bubble manager
        if (this._bubbleManager) {
            this._bubbleManager.destroy();
            this._bubbleManager = null;
        }

        _Scene_Map_terminate.call(this);
    };

    //=============================================================================
    // Game_Event Override
    //=============================================================================

    const _Game_Event_initialize = Game_Event.prototype.initialize;
    Game_Event.prototype.initialize = function(mapId, eventId) {
        _Game_Event_initialize.call(this, mapId, eventId);
        this._bubblesEnabled = this.hasBubbleNotetag();

        if (this._bubblesEnabled) {
            console.log('EventLiquidBubbles: Event initialized with bubbles enabled, ID:', eventId);

            // Register with bubble manager when scene is ready
            setTimeout(() => {
                if (SceneManager._scene && SceneManager._scene._bubbleManager) {
                    const settings = parseBubbleSettings($dataMap.events[eventId]);
                    if (settings) {
                        SceneManager._scene._bubbleManager.addBubblingEvent(eventId, settings);
                    }
                }
            }, 100);
        }
    };

    Game_Event.prototype.hasBubbleNotetag = function() {
        const eventData = $dataMap.events[this.eventId()];
        return eventData && eventData.note &&
               (eventData.note.includes('<bubbles>') || eventData.note.includes('<bubbles:'));
    };

    //=============================================================================
    // Spriteset_Map Helper Methods
    //=============================================================================

    Spriteset_Map.prototype.findTargetSprite = function(target) {
        return this._characterSprites.find(sprite => sprite._character === target);
    };

    //=============================================================================
    // Utility Functions
    //=============================================================================

    // Add a method to check if an event should bubble
    Game_Event.prototype.shouldBubble = function() {
        return this._bubblesEnabled;
    };

    // Debug function to check all events for bubble tags
    window.checkBubbleEvents = function() {
        console.log('EventLiquidBubbles: Checking all events for bubble tags...');
        if (!$dataMap || !$dataMap.events) {
            console.log('EventLiquidBubbles: No map data available');
            return;
        }

        $dataMap.events.forEach((eventData, index) => {
            if (eventData && eventData.note &&
                (eventData.note.includes('<bubbles>') || eventData.note.includes('<bubbles:'))) {
                console.log(`EventLiquidBubbles: Event ${index} has bubble tag:`, eventData.note);
            }
        });
    };

    // Debug function to manually force bubble creation
    window.forceCreateBubbles = function() {
        console.log('EventLiquidBubbles: Manually forcing bubble creation...');
        if (SceneManager._scene && SceneManager._scene._bubbleManager) {
            // Find all events with bubble tags and add them
            $dataMap.events.forEach((eventData, index) => {
                if (eventData && eventData.note &&
                    (eventData.note.includes('<bubbles>') || eventData.note.includes('<bubbles:'))) {
                    const settings = parseBubbleSettings(eventData);
                    if (settings) {
                        SceneManager._scene._bubbleManager.addBubblingEvent(index, settings);
                        console.log(`Added bubbling event ${index} to manager`);
                    }
                }
            });
        }
    };

    // Enhanced Scene_Map integration
    const _Scene_Map_start = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function() {
        _Scene_Map_start.call(this);

        // Initialize bubbling events after scene starts
        setTimeout(() => {
            if (typeof checkBubbleEvents === 'function') {
                checkBubbleEvents();
            }

            // Initialize all bubbling events
            if (this._bubbleManager && $dataMap && $dataMap.events) {
                $dataMap.events.forEach((eventData, index) => {
                    if (eventData && eventData.note &&
                        (eventData.note.includes('<bubbles>') || eventData.note.includes('<bubbles:'))) {
                        const settings = parseBubbleSettings(eventData);
                        if (settings) {
                            this._bubbleManager.addBubblingEvent(index, settings);
                            console.log(`EventLiquidBubbles: Initialized bubbling event ${index}`);
                        }
                    }
                });
            }
        }, 500);
    };

    // Also hook into map refresh to ensure bubbles work after map changes
    const _Game_Map_refresh = Game_Map.prototype.refresh;
    Game_Map.prototype.refresh = function() {
        _Game_Map_refresh.call(this);

        // Refresh bubbling events after map refresh
        setTimeout(() => {
            if (SceneManager._scene && SceneManager._scene._bubbleManager) {
                // Re-initialize bubbling events
                if ($dataMap && $dataMap.events) {
                    $dataMap.events.forEach((eventData, index) => {
                        if (eventData && eventData.note &&
                            (eventData.note.includes('<bubbles>') || eventData.note.includes('<bubbles:'))) {
                            const settings = parseBubbleSettings(eventData);
                            if (settings) {
                                SceneManager._scene._bubbleManager.addBubblingEvent(index, settings);
                            }
                        }
                    });
                }
            }
        }, 100);
    };

    console.log('EventLiquidBubbles: Plugin loaded successfully');

})();
