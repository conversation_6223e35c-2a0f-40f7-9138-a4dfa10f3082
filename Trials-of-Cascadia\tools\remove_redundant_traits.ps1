# Remove redundant "Ex-Parameter Evasion Rate + 0%" traits from armors
# This trait has code:22, dataId:1, value:0 and adds nothing

$filePath = "data/Armors.json"
$content = Get-Content $filePath -Raw

# Pattern to match traits array with only the redundant trait
$pattern = '"traits":\[{"code":22,"dataId":1,"value":0}\]'

# Replace with empty traits array
$replacement = '"traits":[]'

# Perform the replacement
$newContent = $content -replace $pattern, $replacement

# Write back to file
Set-Content -Path $filePath -Value $newContent -NoNewline

Write-Host "Removed redundant 'Ex-Parameter Evasion Rate + 0%' traits from armors."
Write-Host "Items with only this trait now have empty traits arrays."
