/*:
 * @target MZ
 * @plugindesc v2.0.0 Enhanced Stat Reactions
 * @url
 * @help MagicDamageReduction.js
 *
 * This plugin provides comprehensive stat reaction mechanics:
 *
 * 🎯 DEF Absorption (Blue Outline)
 * - Chance to absorb physical damage and debuff attacker
 * - Reduces damage by 50% and applies ATK debuff to attacker
 *
 * 🎯 MDF Absorption (Purple Outline)
 * - Chance to absorb magic damage and convert to TP
 * - Reduces damage by 50% and grants TP based on MDF
 *
 * 🎯 LUK Lucky (Gold Outline)
 * - Chance to reduce any damage to 1
 * - Battle start buff chance
 * - Gold steal chance on normal attacks
 *
 * @param ---DEF Parameters---
 * @text ---DEF Parameters---
 * @type string
 * @default
 *
 * @param defAbsorbChance
 * @text DEF Absorption Base Chance
 * @desc Base percentage chance for DEF absorption
 * @type number
 * @min 0
 * @max 100
 * @default 5
 *
 * @param defAbsorbMultiplier
 * @text DEF Absorption Multiplier
 * @desc How much each point of DEF increases absorption chance
 * @type number
 * @decimals 2
 * @min 0
 * @default 0.15
 *
 * @param defHpGainBase
 * @text DEF HP Gain Base
 * @desc Base HP gained when DEF absorption triggers
 * @type number
 * @min 0
 * @default 15
 *
 * @param defHpGainMultiplier
 * @text DEF HP Gain Multiplier
 * @desc How much each point of DEF increases HP gain
 * @type number
 * @decimals 2
 * @min 0
 * @default 0.2
 *
 * @param ---MDF Parameters---
 * @text ---MDF Parameters---
 * @type string
 * @default
 *
 * @param mdfAbsorbChance
 * @text MDF Absorption Base Chance
 * @desc Base percentage chance for MDF absorption
 * @type number
 * @min 0
 * @max 100
 * @default 5
 *
 * @param mdfAbsorbMultiplier
 * @text MDF Absorption Multiplier
 * @desc How much each point of MDF increases absorption chance
 * @type number
 * @decimals 2
 * @min 0
 * @default 0.15
 *
 * @param mdfTpGainBase
 * @text MDF TP Gain Base
 * @desc Base TP gained when MDF absorption triggers
 * @type number
 * @min 0
 * @default 15
 *
 * @param mdfTpGainMultiplier
 * @text MDF TP Gain Multiplier
 * @desc How much each point of MDF increases TP gain
 * @type number
 * @decimals 2
 * @min 0
 * @default 0.2
 *
 * @param ---LUK Parameters---
 * @text ---LUK Parameters---
 * @type string
 * @default
 *
 * @param lukLuckyChance
 * @text LUK Lucky Base Chance
 * @desc Base percentage chance for lucky damage reduction
 * @type number
 * @min 0
 * @max 100
 * @default 3
 *
 * @param lukLuckyMultiplier
 * @text LUK Lucky Multiplier
 * @desc How much each point of LUK increases lucky chance
 * @type number
 * @decimals 2
 * @min 0
 * @default 0.08
 *

 *
 * @param lukGoldStealChance
 * @text LUK Gold Steal Base Chance
 * @desc Base percentage chance for gold steal on normal attacks
 * @type number
 * @min 0
 * @max 100
 * @default 10
 *
 * @param lukGoldStealMultiplier
 * @text LUK Gold Steal Multiplier
 * @desc How much each point of LUK increases gold steal chance
 * @type number
 * @decimals 2
 * @min 0
 * @default 0.3
 *
 * Terms of Use:
 * Free for commercial and non-commercial use.
 */

(() => {
    'use strict';

    // Global references for RPG Maker MZ
    const { PluginManager, Game_Action, Game_Battler, Scene_Battle, SceneManager, Sprite, Bitmap } =
        window;

    const pluginName = 'MagicDamageReduction';
    const parameters = PluginManager.parameters(pluginName);

    // DEF Parameters
    const defAbsorbChance = Number(parameters['defAbsorbChance'] || 15);
    const defAbsorbMultiplier = Number(parameters['defAbsorbMultiplier'] || 0.3);
    const defAtkDebuffTurns = Number(parameters['defAtkDebuffTurns'] || 3);

    // MDF Parameters
    const mdfAbsorbChance = Number(parameters['mdfAbsorbChance'] || 15);
    const mdfAbsorbMultiplier = Number(parameters['mdfAbsorbMultiplier'] || 0.3);
    const mdfTpGainBase = Number(parameters['mdfTpGainBase'] || 15);
    const mdfTpGainMultiplier = Number(parameters['mdfTpGainMultiplier'] || 0.2);

    // LUK Parameters
    const lukLuckyChance = Number(parameters['lukLuckyChance'] || 8);
    const lukLuckyMultiplier = Number(parameters['lukLuckyMultiplier'] || 0.15);
    const lukBattleStartBuffChance = Number(parameters['lukBattleStartBuffChance'] || 25);
    const lukBattleStartBuffMultiplier = Number(parameters['lukBattleStartBuffMultiplier'] || 0.5);
    const lukGoldStealChance = Number(parameters['lukGoldStealChance'] || 10);
    const lukGoldStealMultiplier = Number(parameters['lukGoldStealMultiplier'] || 0.3);

    // Debug logging for parameter values
    console.log('[MagicDamageReduction] Loaded parameters:');
    console.log(`  lukBattleStartBuffChance: ${lukBattleStartBuffChance}`);
    console.log(`  lukBattleStartBuffMultiplier: ${lukBattleStartBuffMultiplier}`);

    // TEMPORARY OVERRIDE: Uncomment these lines to force 1% chance regardless of plugin parameters
    // const lukBattleStartBuffChance = 1;
    // const lukBattleStartBuffMultiplier = 0;

    // Stat Reactions System
    const StatReactions = {
        // Process DEF absorption for physical damage
        // When triggered, reduces damage by 50% and applies ATK debuff to the attacker
        processDefAbsorption: function (target, value, attacker) {
            const def = target.def;
            const absorbChance = Math.min(100, defAbsorbChance + def * defAbsorbMultiplier);

            if (Math.random() * 100 < absorbChance) {
                target._statReactionType = 'def_absorb';
                target._statReactionAttacker = attacker; // Store attacker reference for debuff
                return Math.floor(value * 0.5); // Reduce damage by 50%
            }
            return value;
        },

        // Process MDF absorption for magic damage
        processMdfAbsorption: function (target, value) {
            const mdf = target.mdf;
            const absorbChance = Math.min(100, mdfAbsorbChance + mdf * mdfAbsorbMultiplier);

            if (Math.random() * 100 < absorbChance) {
                const tpGain = mdfTpGainBase + Math.floor(mdf * mdfTpGainMultiplier);
                target._statReactionType = 'mdf_absorb';
                target._statReactionTpGain = tpGain;
                return Math.floor(value * 0.5); // Reduce damage by 50%
            }
            return value;
        },

        // Process LUK lucky damage reduction
        processLukLucky: function (target, value) {
            const luk = target.luk;
            const luckyChance = Math.min(100, lukLuckyChance + luk * lukLuckyMultiplier);

            if (Math.random() * 100 < luckyChance && value > 1) {
                target._statReactionType = 'luk_lucky';
                return 1; // Reduce damage to 1
            }
            return value;
        },

        // Apply reaction effects after damage calculation
        applyReactionEffects: function (target) {
            // Apply ATK debuff to attacker for DEF absorption
            if (target._statReactionAttacker && target._statReactionType === 'def_absorb') {
                const attacker = target._statReactionAttacker;
                if (attacker && attacker.isAlive()) {
                    // Apply ATK debuff (buff index 2 = ATK in RPG Maker MZ)
                    attacker.addDebuff(2, defAtkDebuffTurns);
                }
                target._statReactionAttacker = null;
            }

            if (target._statReactionTpGain > 0) {
                target.gainTp(target._statReactionTpGain);
                target._statReactionTpGain = 0;
            }
        },
    };

    // Override damage calculation to include stat reactions
    const _Game_Action_makeDamageValue = Game_Action.prototype.makeDamageValue;
    Game_Action.prototype.makeDamageValue = function (target, critical) {
        let value = _Game_Action_makeDamageValue.call(this, target, critical);

        // Only process for actors taking positive damage
        if (target.isActor() && value > 0) {
            // Process LUK lucky damage first (affects all damage types)
            value = StatReactions.processLukLucky(target, value);

            // Process DEF/MDF absorption based on damage type
            if (this.isPhysical()) {
                value = StatReactions.processDefAbsorption(target, value, this.subject());
            } else if (this.isMagical()) {
                value = StatReactions.processMdfAbsorption(target, value);
            }
        }

        return value;
    };

    // Override action application to trigger effects
    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function (target) {
        _Game_Action_apply.call(this, target);
        StatReactions.applyReactionEffects(target);

        // Process LUK gold steal on normal attacks
        if (this.isAttack() && this.subject().isActor() && target.isEnemy()) {
            const actor = this.subject();
            const luk = actor.luk;
            const goldStealChance = Math.min(
                100,
                lukGoldStealChance + luk * lukGoldStealMultiplier
            );

            if (Math.random() * 100 < goldStealChance) {
                // Calculate gold amount
                const enemyLevel = target.level || 1;
                const randomFactor = 0.8 + Math.random() * 0.4; // 0.8 to 1.2
                const gold = Math.max(1, Math.floor(enemyLevel * luk * randomFactor));

                $gameParty.gainGold(gold);

                // Create gold steal popup
                createGoldStealPopup(actor, gold);
            }
        }
    };

    // Color-coded popup system for stat reactions
    function createStatReactionPopup(battler, reactionType) {
        if (!battler) return;

        const scene = SceneManager._scene;
        if (!scene || !scene._spriteset) return;

        // Initialize popup array if needed
        if (!scene._spriteset._statReactionPopupSprites) {
            scene._spriteset._statReactionPopupSprites = [];
        }

        // Create the popup sprite
        const sprite = new Sprite(new Bitmap(160, 60));
        // Add to spriteset instead of scene to maintain proper rendering order
        scene._spriteset.addChild(sprite);
        scene._spriteset._statReactionPopupSprites.push(sprite);

        sprite.bitmap.clear();
        sprite.bitmap.fontFace = $gameSystem.mainFontFace();
        sprite.bitmap.fontSize = 28;
        sprite.bitmap.outlineWidth = 4;
        sprite.bitmap.textColor = '#FFFFFF';

        let text = '';
        let outlineColor = '';

        // Set text and color based on reaction type
        switch (reactionType) {
            case 'def_absorb':
                text = 'GROUNDED!';
                outlineColor = 'rgba(0, 100, 255, 0.8)'; // Blue outline
                break;
            case 'mdf_absorb':
                text = 'ABSORB!';
                outlineColor = 'rgba(153, 102, 204, 0.8)'; // Purple outline
                break;
            case 'luk_lucky':
                text = 'LUCKY!';
                outlineColor = 'rgba(255, 215, 0, 0.8)'; // Gold outline
                break;
            default:
                return; // Unknown reaction type
        }

        sprite.bitmap.outlineColor = outlineColor;
        sprite.bitmap.drawText(text, 0, 0, 160, 60, 'center');

        // Position above the battler
        const battlerSprite = scene._spriteset.battlerSprites().find(s => s._battler === battler);
        if (battlerSprite) {
            sprite.x = battlerSprite.x - 80; // Centered above
            sprite.y = battlerSprite.y - 120; // Raise it above the character
        }

        sprite.opacity = 255;
        let duration = 30; // Frames before fading
        let fadeOutSpeed = 8;

        // Custom update function for animation
        sprite.update = function () {
            if (duration > 0) {
                duration--;
                this.y -= 1; // Float upward
            } else {
                this.opacity -= fadeOutSpeed; // Smooth fade out
                this.y -= 1; // Continue floating
                if (this.opacity <= 0) {
                    this.bitmap.clear();
                    scene._spriteset.removeChild(this);
                    const index = scene._spriteset._statReactionPopupSprites.indexOf(this);
                    if (index >= 0) {
                        scene._spriteset._statReactionPopupSprites.splice(index, 1);
                    }
                }
            }
        };

        // Auto-cleanup after 1.5 seconds as backup
        setTimeout(() => {
            if (sprite.parent) {
                sprite.opacity = 0;
                sprite.bitmap.clear();
                scene._spriteset.removeChild(sprite);
                const index = scene._spriteset._statReactionPopupSprites.indexOf(sprite);
                if (index >= 0) {
                    scene._spriteset._statReactionPopupSprites.splice(index, 1);
                }
            }
        }, 1500);
    }

    // Create gold steal popup
    function createGoldStealPopup(battler, goldAmount) {
        if (!battler) return;

        const scene = SceneManager._scene;
        if (!scene || !scene._spriteset) return;

        // Initialize popup array if needed
        if (!scene._spriteset._goldStealPopupSprites) {
            scene._spriteset._goldStealPopupSprites = [];
        }

        // Create the popup sprite
        const sprite = new Sprite(new Bitmap(160, 60));
        // Add to spriteset instead of scene to maintain proper rendering order
        scene._spriteset.addChild(sprite);
        scene._spriteset._goldStealPopupSprites.push(sprite);

        sprite.bitmap.clear();
        sprite.bitmap.fontFace = $gameSystem.mainFontFace();
        sprite.bitmap.fontSize = 24;
        sprite.bitmap.outlineColor = 'rgba(255, 215, 0, 0.8)'; // Gold outline
        sprite.bitmap.outlineWidth = 4;
        sprite.bitmap.textColor = '#FFFFFF';

        sprite.bitmap.drawText(`+${goldAmount}G`, 0, 0, 160, 60, 'center');

        // Position above the battler
        const battlerSprite = scene._spriteset.battlerSprites().find(s => s._battler === battler);
        if (battlerSprite) {
            sprite.x = battlerSprite.x - 80;
            sprite.y = battlerSprite.y - 100; // Different height than other popups
        }

        sprite.opacity = 255;
        let duration = 40; // Medium duration
        let fadeOutSpeed = 7;

        // Custom update function for animation
        sprite.update = function () {
            if (duration > 0) {
                duration--;
                this.y -= 0.8; // Medium float speed
            } else {
                this.opacity -= fadeOutSpeed;
                this.y -= 0.8;
                if (this.opacity <= 0) {
                    this.bitmap.clear();
                    scene._spriteset.removeChild(this);
                    const index = scene._spriteset._goldStealPopupSprites.indexOf(this);
                    if (index >= 0) {
                        scene._spriteset._goldStealPopupSprites.splice(index, 1);
                    }
                }
            }
        };

        // Auto-cleanup
        setTimeout(() => {
            if (sprite.parent) {
                sprite.opacity = 0;
                sprite.bitmap.clear();
                scene._spriteset.removeChild(sprite);
                const index = scene._spriteset._goldStealPopupSprites.indexOf(sprite);
                if (index >= 0) {
                    scene._spriteset._goldStealPopupSprites.splice(index, 1);
                }
            }
        }, 1800);
    }

    // Override Game_Action.prototype.executeDamage to trigger popups
    const _Game_Action_executeDamage = Game_Action.prototype.executeDamage;
    Game_Action.prototype.executeDamage = function (target, value) {
        _Game_Action_executeDamage.call(this, target, value);

        // Check for stat reaction and create appropriate popup
        if (target._statReactionType) {
            createStatReactionPopup(target, target._statReactionType);
            target._statReactionType = null;
        }
    };

    // Helper function to find actor's highest current stat
    function getHighestStatId(actor) {
        // Try to use getter methods first, fallback to params array if available
        if (actor) {
            // RPG Maker MZ buff indices: 0=ATK, 1=DEF, 2=MAT, 3=MDF, 4=AGI, 5=LUK
            const stats = [
                typeof actor.atk === 'function' ? actor.atk() : actor.atk || (actor.params && actor.params[2]) || 0,  // ATK
                typeof actor.def === 'function' ? actor.def() : actor.def || (actor.params && actor.params[3]) || 0,  // DEF
                typeof actor.mat === 'function' ? actor.mat() : actor.mat || (actor.params && actor.params[4]) || 0,  // MAT
                typeof actor.mdf === 'function' ? actor.mdf() : actor.mdf || (actor.params && actor.params[5]) || 0,  // MDF
                typeof actor.agi === 'function' ? actor.agi() : actor.agi || (actor.params && actor.params[6]) || 0,  // AGI
                typeof actor.luk === 'function' ? actor.luk() : actor.luk || (actor.params && actor.params[7]) || 0   // LUK
            ];

            let highestStatId = 0;
            let highestValue = stats[0];

            for (let i = 1; i < stats.length; i++) {
                if (stats[i] > highestValue) {
                    highestValue = stats[i];
                    highestStatId = i;
                }
            }
            return highestStatId;
        }
        // If actor is missing, log once and default to ATK
        console.warn('[MagicDamageReduction] Actor object missing, defaulting to ATK buff', actor);
        return 0;
    }

    // LUK Battle Start Buff System - Fixed to buff highest stat
    // Enhanced with 60-frame delay (1 second at 60 FPS) before applying buffs
    const _Scene_Battle_start = Scene_Battle.prototype.start;
    Scene_Battle.prototype.start = function () {
        _Scene_Battle_start.call(this);

        // Check if this is a new battle session (not just returning from menu)
        if (!this._battleStartBuffsApplied) {
            // Initialize frame counter for battle start buffs
            this._battleStartBuffFrames = 60; // Wait 60 frames before applying buffs
            this._pendingBuffPopups = []; // Array to handle multiple popups
        }
        // If _battleStartBuffsApplied is already true, don't reset it
    };

    // Override Scene_Battle update to handle frame-based buff timing
    const _Scene_Battle_update = Scene_Battle.prototype.update;
    Scene_Battle.prototype.update = function () {
        _Scene_Battle_update.call(this);

        // Handle battle start buff timing - only if not already applied
        if (this._battleStartBuffFrames > 0 && !this._battleStartBuffsApplied) {
            this._battleStartBuffFrames--;

            if (this._battleStartBuffFrames <= 0) {
                this.applyBattleStartBuffs();
                this._battleStartBuffsApplied = true;
            }
        }

        // Handle battle start popup timing
        if (this._pendingBuffPopups && this._pendingBuffPopups.length > 0) {
            // Update all pending popups
            for (let i = this._pendingBuffPopups.length - 1; i >= 0; i--) {
                const popup = this._pendingBuffPopups[i];
                popup.frames--;

                if (popup.frames <= 0) {
                    createBattleStartBuffPopup(popup.actor, popup.statName);
                    this._pendingBuffPopups.splice(i, 1);
                }
            }
        }
    };

    // Apply battle start buffs after frame delay
    Scene_Battle.prototype.applyBattleStartBuffs = function () {
        // Process LUK battle start buffs for each party member
        $gameParty.members().forEach(actor => {
            // Safety check: ensure actor is properly initialized
            if (!actor || typeof actor.luk === 'undefined') {
                console.warn('[MagicDamageReduction] Actor not properly initialized, skipping buff');
                return;
            }

            const luk = actor.luk;
            const buffChance = Math.min(
                100,
                lukBattleStartBuffChance + luk * lukBattleStartBuffMultiplier
            );

            // Debug logging to help identify why buffs are proccing frequently
            console.log(`[MagicDamageReduction] ${actor.name()} battle start buff check:`);
            console.log(`  Base chance: ${lukBattleStartBuffChance}%`);
            console.log(`  LUK stat: ${luk}`);
            console.log(`  LUK multiplier: ${lukBattleStartBuffMultiplier}`);
            console.log(`  Final chance: ${buffChance.toFixed(2)}%`);

            if (Math.random() * 100 < buffChance) {
                // 🔧 FIX: Buff the actor's highest current stat instead of random
                const baseStatId = getHighestStatId(actor);

                // 🔧 FIX: Convert our stat index to RPG Maker MZ buff index
                // Our indices: 0=ATK, 1=DEF, 2=MAT, 3=MDF, 4=AGI, 5=LUK
                // RPG Maker buff indices: 0=MHP, 1=MMP, 2=ATK, 3=DEF, 4=MAT, 5=MDF, 6=AGI, 7=LUK
                const statId = baseStatId + 2; // Add 2 to skip MHP and MMP

                const turns = 5;

                actor.addBuff(statId, turns);

                // 🔧 FIX: Ensure stat names match the actual buffed stat
                const statNames = ['ATK+', 'DEF+', 'MAT+', 'MDF+', 'AGI+', 'LUK+'];
                const statName = statNames[baseStatId] || 'BUFF+'; // Use baseStatId for display name

                // Create a special popup for battle start buff (frame-based delay)
                this._pendingBuffPopups.push({
                    actor: actor,
                    statName: statName,
                    frames: 12 // 12 frames delay for popup
                });
            }
        });
    };

    // Create battle start buff popup
    function createBattleStartBuffPopup(battler, statName) {
        if (!battler) return;

        const scene = SceneManager._scene;
        if (!scene || !scene._spriteset) return;

        // Initialize popup array if needed
        if (!scene._spriteset._buffPopupSprites) {
            scene._spriteset._buffPopupSprites = [];
        }

        // Create the popup sprite
        const sprite = new Sprite(new Bitmap(160, 60));
        // Add to spriteset instead of scene to maintain proper rendering order
        scene._spriteset.addChild(sprite);
        scene._spriteset._buffPopupSprites.push(sprite);

        sprite.bitmap.clear();
        sprite.bitmap.fontFace = $gameSystem.mainFontFace();
        sprite.bitmap.fontSize = 24;
        sprite.bitmap.outlineColor = 'rgba(255, 215, 0, 0.8)'; // Gold outline
        sprite.bitmap.outlineWidth = 4;
        sprite.bitmap.textColor = '#FFFFFF';

        sprite.bitmap.drawText(statName, 0, 0, 160, 60, 'center');

        // Position above the battler
        const battlerSprite = scene._spriteset.battlerSprites().find(s => s._battler === battler);
        if (battlerSprite) {
            sprite.x = battlerSprite.x - 80;
            sprite.y = battlerSprite.y - 140; // Higher than normal popups
        }

        sprite.opacity = 255;
        let duration = 45; // Longer duration for buff popups
        let fadeOutSpeed = 6;

        // Custom update function for animation
        sprite.update = function () {
            if (duration > 0) {
                duration--;
                this.y -= 0.5; // Slower float upward
            } else {
                this.opacity -= fadeOutSpeed;
                this.y -= 0.5;
                if (this.opacity <= 0) {
                    this.bitmap.clear();
                    scene._spriteset.removeChild(this);
                    const index = scene._spriteset._buffPopupSprites.indexOf(this);
                    if (index >= 0) {
                        scene._spriteset._buffPopupSprites.splice(index, 1);
                    }
                }
            }
        };

        // Auto-cleanup
        setTimeout(() => {
            if (sprite.parent) {
                sprite.opacity = 0;
                sprite.bitmap.clear();
                scene._spriteset.removeChild(sprite);
                const index = scene._spriteset._buffPopupSprites.indexOf(sprite);
                if (index >= 0) {
                    scene._spriteset._buffPopupSprites.splice(index, 1);
                }
            }
        }, 2000);
    }

    // Clean up reaction data on battle end
    const _Game_Battler_onBattleEnd = Game_Battler.prototype.onBattleEnd;
    Game_Battler.prototype.onBattleEnd = function () {
        _Game_Battler_onBattleEnd.call(this);
        this._statReactionType = null;
        this._statReactionAttacker = null;
        this._statReactionTpGain = 0;
    };

    // Clean up battle scene state when battle actually ends
    const _Scene_Battle_terminate = Scene_Battle.prototype.terminate;
    Scene_Battle.prototype.terminate = function () {
        _Scene_Battle_terminate.call(this);
        
        // Reset battle start buff state when battle actually ends
        this._battleStartBuffsApplied = false;
        this._battleStartBuffFrames = 0;
        this._pendingBuffPopups = [];
        
        // Clean up any remaining popup sprites
        if (this._spriteset) {
            if (this._spriteset._statReactionPopupSprites) {
                this._spriteset._statReactionPopupSprites.forEach(sprite => {
                    if (sprite && sprite.parent) {
                        sprite.bitmap.clear();
                        this._spriteset.removeChild(sprite);
                    }
                });
                this._spriteset._statReactionPopupSprites = [];
            }
            
            if (this._spriteset._goldStealPopupSprites) {
                this._spriteset._goldStealPopupSprites.forEach(sprite => {
                    if (sprite && sprite.parent) {
                        sprite.bitmap.clear();
                        this._spriteset.removeChild(sprite);
                    }
                });
                this._spriteset._goldStealPopupSprites = [];
            }
            
            if (this._spriteset._buffPopupSprites) {
                this._spriteset._buffPopupSprites.forEach(sprite => {
                    if (sprite && sprite.parent) {
                        sprite.bitmap.clear();
                        this._spriteset.removeChild(sprite);
                    }
                });
                this._spriteset._buffPopupSprites = [];
            }
        }
    };
})();
