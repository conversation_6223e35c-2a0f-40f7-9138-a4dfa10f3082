/*:
 * @plugindesc Shifts the dialogue window frame and background to the left.
 *
 * @param Shift Amount
 * @desc The amount to shift the dialogue window frame and background (in pixels).
 * @type number
 * @default 10
 *
 * @help This plugin does not provide plugin commands.
 */

(function () {
    let parameters = PluginManager.parameters('TextBufferPlugin');
    let shiftAmount = Number(parameters['Shift Amount'] || 10);

    let _Window_Base_resetFontSettings = Window_Base.prototype.resetFontSettings;
    Window_Base.prototype.resetFontSettings = function () {
        _Window_Base_resetFontSettings.call(this);
        if (this.constructor === Window_Message) {
            this.contents.x += shiftAmount;
        }
    };

    let _Window_Base__refreshBack = Window_Base.prototype._refreshBack;
    Window_Base.prototype._refreshBack = function () {
        _Window_Base__refreshBack.call(this);
        if (this.constructor === Window_Message) {
            this._backSprite.x -= shiftAmount;
        }
    };

    let _Window_Base__refreshFrame = Window_Base.prototype._refreshFrame;
    Window_Base.prototype._refreshFrame = function () {
        _Window_Base__refreshFrame.call(this);
        if (this.constructor === Window_Message) {
            this._frameSprite.x -= shiftAmount;
        }
    };
})();

Graphics.boxWidth += 50;
