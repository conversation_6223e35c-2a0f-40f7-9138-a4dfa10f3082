[null, {"id": 1, "atypeId": 1, "description": "", "etypeId": 4, "traits": [], "iconIndex": 0, "name": "-----<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 2, "atypeId": 2, "description": "Low defense but boost to magic power.", "etypeId": 4, "traits": [], "iconIndex": 1751, "name": "Copper Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 1, 2, 1, 0, 0], "price": 400}, {"id": 3, "atypeId": 2, "description": "Low defense but boost to magic power.", "etypeId": 4, "traits": [], "iconIndex": 1745, "name": "Bronze Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 2, 3, 2, 0, 0], "price": 1000}, {"id": 4, "atypeId": 2, "description": "Low defense but boost to magic power.", "etypeId": 4, "traits": [], "iconIndex": 1752, "name": "Silver Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 3, 4, 3, 0, 0], "price": 2000}, {"id": 5, "atypeId": 2, "description": "Low defense but boost to magic power.", "etypeId": 4, "traits": [], "iconIndex": 1746, "name": "Golden Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 4, 5, 4, 0, 0], "price": 4000}, {"id": 6, "atypeId": 2, "description": "Low defense but boost to magic power.", "etypeId": 4, "traits": [], "iconIndex": 1748, "name": "Platinum Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 5, 6, 5, 0, 0], "price": 6000}, {"id": 7, "atypeId": 2, "description": "Reduces Ice and Water damage by 15%", "etypeId": 4, "traits": [{"code": 11, "dataId": 3, "value": 0.85}, {"code": 11, "dataId": 5, "value": 0.85}], "iconIndex": 1748, "name": "Sapphire Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 32, 16, 0, 0], "price": 2000}, {"id": 8, "atypeId": 2, "description": "Reduces Fire and Lightning damage by 15%", "etypeId": 4, "traits": [{"code": 11, "dataId": 2, "value": 0.85}, {"code": 11, "dataId": 4, "value": 0.85}], "iconIndex": 1744, "name": "Ruby Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 32, 16, 0, 0], "price": 2000}, {"id": 9, "atypeId": 2, "description": "Reduces Earth and Wind damage by 15%", "etypeId": 4, "traits": [{"code": 11, "dataId": 6, "value": 0.85}, {"code": 11, "dataId": 7, "value": 0.85}], "iconIndex": 1747, "name": "Emerald Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 32, 16, 0, 0], "price": 2000}, {"id": 10, "atypeId": 2, "description": "Reduces Light and Dark damage by 15%", "etypeId": 4, "traits": [{"code": 11, "dataId": 8, "value": 0.85}, {"code": 11, "dataId": 9, "value": 0.85}], "iconIndex": 1749, "name": "Amethyst Woven Cloak", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 32, 16, 0, 0], "price": 2000}, {"id": 11, "atypeId": 0, "description": "A mystical cloak woven with ancient arcanum threads, enhancing magical abilities.", "etypeId": 2, "traits": [], "iconIndex": 1744, "name": "<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 8, 8, 8, 0, 0], "price": 8000}, {"id": 12, "atypeId": 0, "description": "A mantle worn by ancient sages, imbued with wisdom and magical power.", "etypeId": 2, "traits": [], "iconIndex": 1745, "name": "Sage's Mantle", "note": "", "params": [0, 0, 0, 12, 12, 12, 0, 0], "price": 12000}, {"id": 13, "atypeId": 0, "description": "A cape woven with enchanted threads that enhance spellcasting abilities.", "etypeId": 2, "traits": [], "iconIndex": 1746, "name": "Spellweave Cape", "note": "", "params": [0, 0, 0, 16, 16, 16, 0, 0], "price": 16000}, {"id": 14, "atypeId": 0, "description": "A robe that seems to exist between worlds, enhancing ethereal magic.", "etypeId": 2, "traits": [], "iconIndex": 1747, "name": "Ethereal Robe", "note": "", "params": [0, 0, 0, 20, 20, 20, 0, 0], "price": 20000}, {"id": 15, "atypeId": 0, "description": "A wrap that shimmers with aurora light, enhancing light-based magic.", "etypeId": 2, "traits": [], "iconIndex": 1748, "name": "Aurora Wrap", "note": "", "params": [0, 0, 0, 24, 24, 24, 0, 0], "price": 24000}, {"id": 16, "atypeId": 0, "description": "A drape worn by oracles, enhancing divination and prophetic magic.", "etypeId": 2, "traits": [], "iconIndex": 1749, "name": "Oracle's <PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 28, 28, 28, 0, 0], "price": 28000}, {"id": 17, "atypeId": 0, "description": "A shawl worn by master sorcerers, enhancing destructive magic.", "etypeId": 2, "traits": [], "iconIndex": 1750, "name": "Sorcerer's <PERSON><PERSON>", "note": "", "params": [0, 0, 0, 32, 32, 32, 0, 0], "price": 32000}, {"id": 18, "atypeId": 0, "description": "A shroud woven with pure arcanum, the ultimate magical protection.", "etypeId": 2, "traits": [], "iconIndex": 1751, "name": "<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 36, 36, 36, 0, 0], "price": 36000}, {"id": 19, "atypeId": 0, "description": "A cloak that contains the essence of a nebula, enhancing cosmic magic.", "etypeId": 2, "traits": [], "iconIndex": 1752, "name": "Nebula Cloak", "note": "", "params": [0, 0, 0, 40, 40, 40, 0, 0], "price": 40000}, {"id": 20, "atypeId": 1, "description": "", "etypeId": 4, "traits": [], "iconIndex": 0, "name": "-----<PERSON><PERSON>", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 21, "atypeId": 3, "description": "<ColorLock>Light and easy to move in, medium defense.</ColorLock>", "etypeId": 4, "traits": [], "iconIndex": 1687, "name": "Copper Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 2, 0, 1, 1, 0], "price": 400}, {"id": 22, "atypeId": 3, "description": "<ColorLock>Light and easy to move in, medium defense.</ColorLock>", "etypeId": 4, "traits": [], "iconIndex": 1681, "name": "Bronze Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 3, 0, 2, 2, 0], "price": 1000}, {"id": 23, "atypeId": 3, "description": "<ColorLock>Light and easy to move in, medium defense.</ColorLock>", "etypeId": 4, "traits": [], "iconIndex": 1688, "name": "Silver Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 4, 0, 3, 3, 0], "price": 2000}, {"id": 24, "atypeId": 3, "description": "<ColorLock>Light and easy to move in, medium defense.</ColorLock>", "etypeId": 4, "traits": [], "iconIndex": 1682, "name": "Golden Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 5, 0, 4, 4, 0], "price": 4000}, {"id": 25, "atypeId": 3, "description": "<ColorLock>Light and easy to move in, medium defense.</ColorLock>", "etypeId": 4, "traits": [], "iconIndex": 1684, "name": "Platinum Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 6, 0, 5, 5, 0], "price": 6000}, {"id": 26, "atypeId": 3, "description": "A tunic woven with sapphire threads, reducing water and ice damage by 15%.", "etypeId": 4, "traits": [{"code": 11, "dataId": 3, "value": 0.85}, {"code": 11, "dataId": 5, "value": 0.85}], "iconIndex": 1684, "name": "Sapphire Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 16, 0], "price": 2000}, {"id": 27, "atypeId": 3, "description": "A tunic woven with ruby threads, reducing fire and lightning damage by 15%.", "etypeId": 4, "traits": [{"code": 11, "dataId": 2, "value": 0.85}, {"code": 11, "dataId": 4, "value": 0.85}], "iconIndex": 1680, "name": "Ruby Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 16, 0], "price": 2000}, {"id": 28, "atypeId": 3, "description": "A tunic woven with emerald threads, reducing earth and wind damage by 15%.", "etypeId": 4, "traits": [{"code": 11, "dataId": 6, "value": 0.85}, {"code": 11, "dataId": 7, "value": 0.85}], "iconIndex": 1683, "name": "Emerald Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 16, 0], "price": 2000}, {"id": 29, "atypeId": 3, "description": "A tunic woven with amethyst threads, reducing light and dark damage by 15%.", "etypeId": 4, "traits": [{"code": 11, "dataId": 8, "value": 0.85}, {"code": 11, "dataId": 9, "value": 0.85}], "iconIndex": 1685, "name": "Amethyst Tunic", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 16, 0], "price": 2000}, {"id": 30, "atypeId": 0, "description": "A mantle that flows like the wind, enhancing agility and movement.", "etypeId": 2, "traits": [], "iconIndex": 1680, "name": "<PERSON>", "note": "", "params": [0, 0, 0, 12, 0, 8, 8, 0], "price": 8000}, {"id": 31, "atypeId": 0, "description": "A wrap worn by wardens, providing protection and vigilance.", "etypeId": 2, "traits": [], "iconIndex": 1681, "name": "Warden's Wrap", "note": "", "params": [0, 0, 0, 16, 0, 12, 12, 0], "price": 12000}, {"id": 32, "atypeId": 0, "description": "A vest that seems to phase between reality and shadow.", "etypeId": 2, "traits": [], "iconIndex": 1682, "name": "Phantom Vest", "note": "", "params": [0, 0, 0, 20, 0, 16, 16, 0], "price": 16000}, {"id": 33, "atypeId": 0, "description": "Clothing designed for travelers, offering protection on long journeys.", "etypeId": 2, "traits": [], "iconIndex": 1683, "name": "Nomad's Garb", "note": "", "params": [0, 0, 0, 24, 0, 20, 20, 0], "price": 20000}, {"id": 34, "atypeId": 0, "description": "A coat designed for sailors, offering protection against the elements.", "etypeId": 2, "traits": [], "iconIndex": 1684, "name": "Mariner's Coat", "note": "", "params": [0, 0, 0, 28, 0, 24, 24, 0], "price": 24000}, {"id": 35, "atypeId": 0, "description": "Light armor designed for hunters and trackers, enhancing stealth.", "etypeId": 2, "traits": [], "iconIndex": 1685, "name": "Tracker's Gear", "note": "", "params": [0, 0, 0, 32, 0, 28, 28, 0], "price": 28000}, {"id": 36, "atypeId": 0, "description": "A hauberk favored by bandits, offering stealth and protection.", "etypeId": 2, "traits": [], "iconIndex": 1686, "name": "Bandit's Hauberk", "note": "", "params": [0, 0, 0, 36, 0, 32, 32, 0], "price": 32000}, {"id": 37, "atypeId": 0, "description": "A jerkin crafted from drake hide, offering exceptional protection.", "etypeId": 2, "traits": [], "iconIndex": 1687, "name": "<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 40, 0, 36, 36, 0], "price": 36000}, {"id": 38, "atypeId": 0, "description": "A cuirass that embodies the courage and strength of a lion.", "etypeId": 2, "traits": [], "iconIndex": 1688, "name": "<PERSON><PERSON>", "note": "", "params": [0, 0, 0, 44, 0, 40, 40, 0], "price": 40000}, {"id": 39, "atypeId": 0, "description": "A tabard woven with magical glyphs, enhancing protective magic.", "etypeId": 2, "traits": [], "iconIndex": 1680, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 48, 0, 44, 44, 0], "price": 45000}, {"id": 40, "atypeId": 1, "description": "", "etypeId": 4, "traits": [], "iconIndex": 135, "name": "-----<PERSON><PERSON>", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 41, "atypeId": 4, "description": "High defense but slightly harder to move in.", "etypeId": 4, "traits": [], "iconIndex": 1719, "name": "Copper Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 3, 0, 3, -1, 0], "price": 400}, {"id": 42, "atypeId": 4, "description": "High defense but slightly harder to move in.", "etypeId": 4, "traits": [], "iconIndex": 1713, "name": "Bronze Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 5, 0, 5, -2, 0], "price": 1000}, {"id": 43, "atypeId": 4, "description": "High defense but slightly harder to move in.", "etypeId": 4, "traits": [], "iconIndex": 137, "name": "Silver Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 7, 0, 7, -3, 0], "price": 2000}, {"id": 44, "atypeId": 4, "description": "High defense but slightly harder to move in.", "etypeId": 4, "traits": [], "iconIndex": 1714, "name": "Golden Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 9, 0, 9, -4, 0], "price": 4000}, {"id": 45, "atypeId": 4, "description": "High defense but slightly harder to move in.", "etypeId": 4, "traits": [], "iconIndex": 1716, "name": "Platinum Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 11, 0, 11, -5, 0], "price": 6000}, {"id": 46, "atypeId": 4, "description": "Platemail crafted with sapphire, reducing water and ice damage by 15%.", "etypeId": 4, "traits": [{"code": 11, "dataId": 3, "value": 0.85}, {"code": 11, "dataId": 5, "value": 0.85}], "iconIndex": 1716, "name": "Sapphire Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [1600, 0, 0, 48, 0, 24, -16, 0], "price": 2000}, {"id": 47, "atypeId": 4, "description": "Platemail crafted with ruby, reducing fire and lightning damage by 15%.", "etypeId": 4, "traits": [{"code": 11, "dataId": 2, "value": 0.85}, {"code": 11, "dataId": 4, "value": 0.85}], "iconIndex": 1712, "name": "Ruby Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [1600, 0, 0, 48, 0, 24, -16, 0], "price": 2000}, {"id": 48, "atypeId": 4, "description": "Platemail crafted with emerald, reducing earth and wind damage by 15%.", "etypeId": 4, "traits": [{"code": 11, "dataId": 6, "value": 0.85}, {"code": 11, "dataId": 7, "value": 0.85}], "iconIndex": 1715, "name": "Emerald Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [1600, 0, 0, 48, 0, 24, -16, 0], "price": 2000}, {"id": 49, "atypeId": 4, "description": "Platemail crafted with amethyst, reducing light and dark damage by 15%.", "etypeId": 4, "traits": [{"code": 11, "dataId": 8, "value": 0.85}, {"code": 11, "dataId": 9, "value": 0.85}], "iconIndex": 1717, "name": "Amethyst Platemail", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [1600, 0, 0, 48, 0, 24, 0, -16], "price": 2000}, {"id": 50, "atypeId": 0, "description": "Plate armor blessed by divine forces, offering holy protection.", "etypeId": 2, "traits": [], "iconIndex": 1712, "name": "Blessed Plate", "note": "", "params": [0, 0, 0, 16, 0, 8, 0, 0], "price": 8000}, {"id": 51, "atypeId": 0, "description": "Mail armor that serves as an impenetrable bastion of defense.", "etypeId": 2, "traits": [], "iconIndex": 1713, "name": "Bastion Mail", "note": "", "params": [0, 0, 0, 20, 0, 12, 0, 0], "price": 12000}, {"id": 52, "atypeId": 0, "description": "A suit of armor forged with master craftsmanship and care.", "etypeId": 2, "traits": [], "iconIndex": 1714, "name": "Forged Suit", "note": "", "params": [0, 0, 0, 24, 0, 16, 0, 0], "price": 16000}, {"id": 53, "atypeId": 0, "description": "Armor crafted from darkiron, offering shadowy protection.", "etypeId": 2, "traits": [], "iconIndex": 1715, "name": "Darkiron Armor", "note": "", "params": [0, 0, 0, 28, 0, 20, 0, 0], "price": 20000}, {"id": 54, "atypeId": 0, "description": "A harness born from steel, offering unyielding defense.", "etypeId": 2, "traits": [], "iconIndex": 1716, "name": "Steelborn Harness", "note": "", "params": [0, 0, 0, 32, 0, 24, 0, 0], "price": 24000}, {"id": 55, "atypeId": 0, "description": "The aegis of a templar, offering divine protection and strength.", "etypeId": 2, "traits": [], "iconIndex": 1717, "name": "<PERSON><PERSON><PERSON>'s A<PERSON>is", "note": "", "params": [0, 0, 0, 36, 0, 28, 0, 0], "price": 28000}, {"id": 56, "atypeId": 0, "description": "Raiment designed for the warfront, offering battlefield protection.", "etypeId": 2, "traits": [], "iconIndex": 1718, "name": "Warfront Raiment", "note": "", "params": [0, 0, 0, 40, 0, 32, 0, 0], "price": 32000}, {"id": 57, "atypeId": 0, "description": "A bastion that embodies the valor of legendary warriors.", "etypeId": 2, "traits": [], "iconIndex": 1719, "name": "Valiant's Bast<PERSON>", "note": "", "params": [0, 0, 0, 44, 0, 36, 0, 0], "price": 36000}, {"id": 58, "atypeId": 0, "description": "A shell as strong as a fortress, offering impenetrable defense.", "etypeId": 2, "traits": [], "iconIndex": 1712, "name": "Fortress Shell", "note": "", "params": [0, 0, 0, 48, 0, 40, 0, 0], "price": 40000}, {"id": 59, "atypeId": 0, "description": "A carapace worthy of titans, offering legendary protection.", "etypeId": 2, "traits": [], "iconIndex": 1713, "name": "Titan's Carapace", "note": "", "params": [0, 0, 0, 52, 0, 44, 0, 0], "price": 45000}, {"id": 60, "atypeId": 1, "description": "", "etypeId": 4, "traits": [], "iconIndex": 0, "name": "-----<PERSON><PERSON>", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 61, "atypeId": 2, "description": "Boosts Magic Power.", "etypeId": 3, "traits": [], "iconIndex": 1671, "name": "Copper Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 1, 1, 1, 0, 0], "price": 300}, {"id": 62, "atypeId": 2, "description": "Boosts Magic Power.", "etypeId": 3, "traits": [], "iconIndex": 1665, "name": "Bronze Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 2, 2, 2, 0, 0], "price": 800}, {"id": 63, "atypeId": 2, "description": "Boosts Magic Power.", "etypeId": 3, "traits": [], "iconIndex": 1672, "name": "Silver Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 3, 3, 3, 0, 0], "price": 1500}, {"id": 64, "atypeId": 2, "description": "Boosts Magic Power.", "etypeId": 3, "traits": [], "iconIndex": 1666, "name": "Golden Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 4, 4, 4, 0, 0], "price": 3000}, {"id": 65, "atypeId": 2, "description": "Boosts Magic Power.", "etypeId": 3, "traits": [], "iconIndex": 1668, "name": "Platinum Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 5, 5, 5, 0, 0], "price": 4500}, {"id": 66, "atypeId": 2, "description": "A cowl woven with sapphire threads, reducing water and ice damage by 10%.", "etypeId": 3, "traits": [{"code": 11, "dataId": 3, "value": 0.9}, {"code": 11, "dataId": 5, "value": 0.9}], "iconIndex": 1668, "name": "Sapphire Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 16, 16, 0, 0], "price": 1560}, {"id": 67, "atypeId": 2, "description": "A cowl woven with ruby threads, reducing fire and lightning damage by 10%.", "etypeId": 3, "traits": [{"code": 11, "dataId": 2, "value": 0.9}, {"code": 11, "dataId": 4, "value": 0.9}], "iconIndex": 1664, "name": "Ruby Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 16, 16, 0, 0], "price": 1560}, {"id": 68, "atypeId": 2, "description": "A cowl woven with emerald threads, reducing earth and wind damage by 10%.", "etypeId": 3, "traits": [{"code": 11, "dataId": 6, "value": 0.9}, {"code": 11, "dataId": 7, "value": 0.9}], "iconIndex": 1667, "name": "Emerald Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 16, 16, 0, 0], "price": 1560}, {"id": 69, "atypeId": 2, "description": "A cowl woven with amethyst threads, reducing light and dark damage by 10%.", "etypeId": 3, "traits": [{"code": 11, "dataId": 8, "value": 0.9}, {"code": 11, "dataId": 9, "value": 0.9}], "iconIndex": 1669, "name": "Amethyst Cowl", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 16, 16, 0, 0], "price": 1560}, {"id": 70, "atypeId": 0, "description": "A hood woven with aether, enhancing ethereal magic and perception.", "etypeId": 2, "traits": [], "iconIndex": 1664, "name": "<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 8, 8, 8, 0, 0], "price": 8000}, {"id": 71, "atypeId": 0, "description": "A cowl worn by visionaries, enhancing foresight and divination.", "etypeId": 2, "traits": [], "iconIndex": 1665, "name": "Visionary's Cowl", "note": "", "params": [0, 0, 0, 12, 12, 12, 0, 0], "price": 12000}, {"id": 72, "atypeId": 0, "description": "A hood that weaves starlight, enhancing celestial magic.", "etypeId": 2, "traits": [], "iconIndex": 1666, "name": "Starweaver's Hood", "note": "", "params": [0, 0, 0, 16, 16, 16, 0, 0], "price": 16000}, {"id": 73, "atypeId": 0, "description": "A capuche worn by seers, enhancing prophetic abilities.", "etypeId": 2, "traits": [], "iconIndex": 1667, "name": "Seer's <PERSON><PERSON>", "note": "", "params": [0, 0, 0, 20, 20, 20, 0, 0], "price": 20000}, {"id": 74, "atypeId": 0, "description": "A cowl worn by master enchanters, enhancing magical enhancement.", "etypeId": 2, "traits": [], "iconIndex": 1668, "name": "Enchanter's Cowl", "note": "", "params": [0, 0, 0, 24, 24, 24, 0, 0], "price": 24000}, {"id": 75, "atypeId": 0, "description": "A hood woven with wisps, enhancing ethereal and spirit magic.", "etypeId": 2, "traits": [], "iconIndex": 1669, "name": "Wisp-<PERSON><PERSON><PERSON> Hood", "note": "", "params": [0, 0, 0, 28, 28, 28, 0, 0], "price": 28000}, {"id": 76, "atypeId": 0, "description": "A cover worn by master arcanists, enhancing arcane magic.", "etypeId": 2, "traits": [], "iconIndex": 1670, "name": "Arcanist's Cover", "note": "", "params": [0, 0, 0, 32, 32, 32, 0, 0], "price": 32000}, {"id": 77, "atypeId": 0, "description": "A cowl worn by lorekeepers, enhancing knowledge and wisdom.", "etypeId": 2, "traits": [], "iconIndex": 1671, "name": "Lorekeeper's Cowl", "note": "", "params": [0, 0, 0, 36, 36, 36, 0, 0], "price": 36000}, {"id": 78, "atypeId": 0, "description": "A veil that bends time itself, enhancing temporal magic.", "etypeId": 2, "traits": [], "iconIndex": 1672, "name": "Timebender's Veil", "note": "", "params": [0, 0, 0, 40, 40, 40, 0, 0], "price": 40000}, {"id": 79, "atypeId": 0, "description": "A headdress that channels eidolon power, enhancing spirit magic.", "etypeId": 2, "traits": [], "iconIndex": 1670, "name": "Eidolon Headdress", "note": "", "params": [0, 0, 0, 44, 44, 44, 0, 0], "price": 45000}, {"id": 80, "atypeId": 1, "description": "", "etypeId": 4, "traits": [], "iconIndex": 1623, "name": "-----<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 100}, {"id": 81, "atypeId": 4, "description": "Increases defense.", "etypeId": 3, "traits": [], "iconIndex": 1639, "name": "Copper Helmet", "note": "<sockets: ALL>\n<rarityEligible>\n", "params": [0, 0, 0, 2, 0, 1, 0, 0], "price": 300}, {"id": 82, "atypeId": 4, "description": "Increases defense.", "etypeId": 3, "traits": [], "iconIndex": 1633, "name": "Bronze Helmet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 4, 0, 2, 0, 0], "price": 800}, {"id": 83, "atypeId": 4, "description": "Increases defense.", "etypeId": 3, "traits": [], "iconIndex": 1640, "name": "Silver Helmet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 6, 0, 3, 0, 0], "price": 1500}, {"id": 84, "atypeId": 4, "description": "Increases defense.", "etypeId": 3, "traits": [], "iconIndex": 1634, "name": "Golden Helmet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 8, 0, 4, 0, 0], "price": 3000}, {"id": 85, "atypeId": 4, "description": "Increases defense.", "etypeId": 3, "traits": [], "iconIndex": 1636, "name": "Platinum Helmet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 24, 0, 12, 0, 0], "price": 4500}, {"id": 86, "atypeId": 4, "description": "A helmet crafted with sapphire, reducing water and ice damage by 10%.", "etypeId": 3, "traits": [{"code": 11, "dataId": 3, "value": 0.9}, {"code": 11, "dataId": 5, "value": 0.9}], "iconIndex": 1636, "name": "Sapphire Helmet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 0, 0], "price": 1560}, {"id": 87, "atypeId": 4, "description": "A helmet crafted with ruby, reducing fire and lightning damage by 10%.", "etypeId": 3, "traits": [{"code": 11, "dataId": 2, "value": 0.9}, {"code": 11, "dataId": 4, "value": 0.9}], "iconIndex": 1632, "name": "<PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 0, 0], "price": 1560}, {"id": 88, "atypeId": 4, "description": "A helmet crafted with emerald, reducing earth and wind damage by 10%.", "etypeId": 3, "traits": [{"code": 11, "dataId": 6, "value": 0.9}, {"code": 11, "dataId": 7, "value": 0.9}], "iconIndex": 1635, "name": "<PERSON> Helmet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 0, 0], "price": 1560}, {"id": 89, "atypeId": 4, "description": "A helmet crafted with amethyst, reducing light and dark damage by 10%.", "etypeId": 3, "traits": [{"code": 11, "dataId": 8, "value": 0.9}, {"code": 11, "dataId": 9, "value": 0.9}], "iconIndex": 1637, "name": "Amethys<PERSON> Helmet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 0, 0], "price": 1560}, {"id": 90, "atypeId": 0, "description": "An aventail that echoes with ancient power, enhancing defense.", "etypeId": 2, "traits": [], "iconIndex": 1632, "name": "Echo Aventail", "note": "", "params": [0, 0, 0, 12, 0, 8, 0, 0], "price": 8000}, {"id": 91, "atypeId": 0, "description": "A coif worn by vanguard warriors, offering frontline protection.", "etypeId": 2, "traits": [], "iconIndex": 1633, "name": "Vanguard Coif", "note": "", "params": [0, 0, 0, 16, 0, 12, 0, 0], "price": 12000}, {"id": 92, "atypeId": 0, "description": "A visor inscribed with runes, enhancing magical protection.", "etypeId": 2, "traits": [], "iconIndex": 1634, "name": "<PERSON><PERSON><PERSON> Visor", "note": "", "params": [0, 0, 0, 20, 0, 16, 0, 0], "price": 16000}, {"id": 93, "atypeId": 0, "description": "A barbute crafted from mithril, offering legendary defense.", "etypeId": 2, "traits": [], "iconIndex": 1635, "name": "<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 24, 0, 20, 0, 0], "price": 20000}, {"id": 94, "atypeId": 0, "description": "A basinet forged in the sky, offering celestial protection.", "etypeId": 2, "traits": [], "iconIndex": 1636, "name": "Skyforge Basinet", "note": "", "params": [0, 0, 0, 28, 0, 24, 0, 0], "price": 24000}, {"id": 95, "atypeId": 0, "description": "A faceplate worn by praetors, offering imperial protection.", "etypeId": 2, "traits": [], "iconIndex": 1637, "name": "Praetor Faceplate", "note": "", "params": [0, 0, 0, 32, 0, 28, 0, 0], "price": 28000}, {"id": 96, "atypeId": 0, "description": "A crest worn by drakeguards, offering dragon-scale protection.", "etypeId": 2, "traits": [], "iconIndex": 1638, "name": "Drakeguard Crest", "note": "", "params": [0, 0, 0, 36, 0, 32, 0, 0], "price": 32000}, {"id": 97, "atypeId": 0, "description": "A mask that strikes fear into enemies, offering dread protection.", "etypeId": 2, "traits": [], "iconIndex": 1639, "name": "Dreadnought Mask", "note": "", "params": [0, 0, 0, 40, 0, 36, 0, 0], "price": 36000}, {"id": 98, "atypeId": 0, "description": "A casque worn by oathbearers, offering sworn protection.", "etypeId": 2, "traits": [], "iconIndex": 1640, "name": "Oathbearer Casque", "note": "", "params": [0, 0, 0, 44, 0, 40, 0, 0], "price": 40000}, {"id": 99, "atypeId": 0, "description": "A galea that crowns the shield, offering royal protection.", "etypeId": 2, "traits": [], "iconIndex": 1632, "name": "Crownshield Galea", "note": "", "params": [0, 0, 0, 48, 0, 44, 0, 0], "price": 45000}, {"id": 100, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "-----<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 101, "atypeId": 5, "description": "Increases defense.", "etypeId": 2, "traits": [], "iconIndex": 1831, "name": "Copper Bracer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 1, 0, 2, 0, 0], "price": 300}, {"id": 102, "atypeId": 5, "description": "Increases defense.", "etypeId": 2, "traits": [], "iconIndex": 1825, "name": "Bronze Bracer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 2, 0, 4, 0, 0], "price": 800}, {"id": 103, "atypeId": 5, "description": "Increases defense.", "etypeId": 2, "traits": [], "iconIndex": 1832, "name": "Silver Bracer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 3, 0, 6, 0, 0], "price": 1500}, {"id": 104, "atypeId": 5, "description": "Increases defense.", "etypeId": 2, "traits": [], "iconIndex": 1826, "name": "Golden Bracer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 4, 0, 8, 0, 0], "price": 3000}, {"id": 105, "atypeId": 5, "description": "Increases defense.", "etypeId": 2, "traits": [], "iconIndex": 1828, "name": "Platinum Bracer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 12, 0, 24, 0, 0], "price": 4500}, {"id": 106, "atypeId": 5, "description": "A bracer crafted with aquamarine, reducing water and ice damage by 8%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 3, "value": 0.92}, {"code": 11, "dataId": 5, "value": 0.92}], "iconIndex": 1508, "name": "Aquamarine Bracer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 16, 0, 16, 16, 0, 0], "price": 2000}, {"id": 107, "atypeId": 5, "description": "A bracer crafted with ruby, reducing fire and lightning damage by 8%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 2, "value": 0.92}, {"code": 11, "dataId": 4, "value": 0.92}], "iconIndex": 1504, "name": "<PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 16, 0, 16, 16, 0, 0], "price": 2000}, {"id": 108, "atypeId": 7, "description": "A bracer crafted with emerald, reducing earth and wind damage by 8%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 6, "value": 0.92}, {"code": 11, "dataId": 7, "value": 0.92}], "iconIndex": 1507, "name": "Emerald Bracer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 16, 0, 16, 16, 0, 0], "price": 2000}, {"id": 109, "atypeId": 7, "description": "A bracer crafted with amethyst, reducing light and dark damage by 8%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 8, "value": 0.92}, {"code": 11, "dataId": 9, "value": 0.92}], "iconIndex": 1509, "name": "Amethyst Bracer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 16, 0, 16, 16, 0, 0], "price": 2000}, {"id": 110, "atypeId": 5, "description": "Increases PWR gained from single TP offensive elemental spells by 12%", "etypeId": 2, "traits": [], "iconIndex": 143, "name": "Power Bracer", "note": "", "params": [0, 0, 0, 3, 0, 6, 0, 0], "price": 3000}, {"id": 111, "atypeId": 0, "description": "A bracer that guards against mana drain, enhancing magical defense.", "etypeId": 2, "traits": [], "iconIndex": 1824, "name": "Mana Guard", "note": "", "params": [0, 0, 0, 8, 0, 8, 0, 0], "price": 8000}, {"id": 112, "atypeId": 0, "description": "A cuff that enhances spellcasting, improving magical accuracy.", "etypeId": 2, "traits": [], "iconIndex": 1825, "name": "Spell Cuff", "note": "", "params": [0, 0, 0, 12, 0, 12, 0, 0], "price": 12000}, {"id": 113, "atypeId": 0, "description": "A vambrace that channels arcane power, enhancing magical strength.", "etypeId": 2, "traits": [], "iconIndex": 1826, "name": "<PERSON><PERSON>", "note": "", "params": [0, 0, 0, 16, 0, 16, 0, 0], "price": 16000}, {"id": 114, "atypeId": 0, "description": "A bracer that channels ether, enhancing ethereal magic.", "etypeId": 2, "traits": [], "iconIndex": 1827, "name": "<PERSON><PERSON>", "note": "", "params": [0, 0, 0, 20, 0, 20, 0, 0], "price": 20000}, {"id": 115, "atypeId": 0, "description": "A buckle that secures spells, enhancing magical stability.", "etypeId": 2, "traits": [], "iconIndex": 1828, "name": "Spell Buckle", "note": "", "params": [0, 0, 0, 24, 0, 24, 0, 0], "price": 24000}, {"id": 116, "atypeId": 0, "description": "A wrist guard inscribed with runes, enhancing magical protection.", "etypeId": 2, "traits": [], "iconIndex": 1829, "name": "<PERSON><PERSON>", "note": "", "params": [0, 0, 0, 28, 0, 28, 0, 0], "price": 28000}, {"id": 117, "atypeId": 0, "description": "A band worn by witches, enhancing hex and curse magic.", "etypeId": 2, "traits": [], "iconIndex": 1830, "name": "Witch Band", "note": "", "params": [0, 0, 0, 32, 0, 32, 0, 0], "price": 32000}, {"id": 118, "atypeId": 0, "description": "A ward that protects against hexes, enhancing magical resistance.", "etypeId": 2, "traits": [], "iconIndex": 1831, "name": "He<PERSON>", "note": "", "params": [0, 0, 0, 36, 0, 36, 0, 0], "price": 36000}, {"id": 119, "atypeId": 0, "description": "A bangle that radiates magic, enhancing all magical abilities.", "etypeId": 2, "traits": [], "iconIndex": 1832, "name": "Magic Bangle", "note": "", "params": [0, 0, 0, 40, 0, 40, 0, 0], "price": 40000}, {"id": 120, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "-----<PERSON><PERSON><PERSON>", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 121, "atypeId": 7, "description": "<ColorLock>Increases Attack and Magic Power.</ColorLock>", "etypeId": 2, "traits": [], "iconIndex": 1566, "name": "Copper Libram", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 1, 0, 1, 1, 0, 0], "price": 300}, {"id": 122, "atypeId": 7, "description": "<ColorLock>Increases Attack and Magic Power.</ColorLock>", "etypeId": 2, "traits": [], "iconIndex": 1554, "name": "Bronze Libram", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 2, 0, 2, 2, 0, 0], "price": 800}, {"id": 123, "atypeId": 7, "description": "<ColorLock>Increases Attack and Magic Power.</ColorLock>", "etypeId": 2, "traits": [], "iconIndex": 1567, "name": "Silver Libram", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 3, 0, 3, 3, 0, 0], "price": 1500}, {"id": 124, "atypeId": 7, "description": "<ColorLock>Increases Attack and Magic Power.</ColorLock>", "etypeId": 2, "traits": [], "iconIndex": 1555, "name": "Golden Libram", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 4, 0, 4, 4, 0, 0], "price": 3000}, {"id": 125, "atypeId": 7, "description": "<ColorLock>Increases Attack and Magic Power.</ColorLock>", "etypeId": 2, "traits": [], "iconIndex": 1561, "name": "Platinum Libram", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 12, 0, 12, 12, 0, 0], "price": 4500}, {"id": 126, "atypeId": 7, "description": "A libram crafted with sapphire, reducing water and ice damage by 8%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 3, "value": 0.92}, {"code": 11, "dataId": 5, "value": 0.92}], "iconIndex": 1559, "name": "Sapphire Libram", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 16, 0, 16, 16, 0, 0], "price": 2000}, {"id": 127, "atypeId": 7, "description": "A libram crafted with ruby, reducing fire and lightning damage by 8%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 2, "value": 0.92}, {"code": 11, "dataId": 4, "value": 0.92}], "iconIndex": 1553, "name": "<PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 16, 0, 16, 16, 0, 0], "price": 2000}, {"id": 128, "atypeId": 7, "description": "A libram crafted with emerald, reducing earth and wind damage by 8%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 6, "value": 0.92}, {"code": 11, "dataId": 7, "value": 0.92}], "iconIndex": 1557, "name": "Emerald Libram", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 16, 0, 16, 16, 0, 0], "price": 2000}, {"id": 129, "atypeId": 7, "description": "A libram crafted with amethyst, reducing light and dark damage by 8%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 8, "value": 0.92}, {"code": 11, "dataId": 9, "value": 0.92}], "iconIndex": 1562, "name": "Amethyst Libram", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 16, 0, 16, 16, 0, 0], "price": 2000}, {"id": 130, "atypeId": 7, "description": "<ColorLock>Increases Attack and Magic Power.</ColorLock> Increase the effect of Mercy by an additional 40%", "etypeId": 2, "traits": [], "iconIndex": 2118, "name": "Libram of Mercy", "note": "", "params": [0, 0, 3, 0, 3, 3, 0, 0], "price": 3000}, {"id": 131, "atypeId": 7, "description": "<ColorLock>Increases Attack and Magic Power.</ColorLock> Increase the effect of Justice by an additional 40%", "etypeId": 2, "traits": [], "iconIndex": 2112, "name": "Libram of Justice", "note": "", "params": [0, 0, 3, 0, 3, 3, 0, 0], "price": 3000}, {"id": 132, "atypeId": 0, "description": "A codex containing ancient wisdom, enhancing knowledge and insight.", "etypeId": 2, "traits": [], "iconIndex": 1553, "name": "Wisdom Codex", "note": "", "params": [0, 0, 0, 8, 8, 8, 0, 0], "price": 8000}, {"id": 133, "atypeId": 0, "description": "Annals that reveal truth, enhancing perception and clarity.", "etypeId": 2, "traits": [], "iconIndex": 1554, "name": "Truth's Annals", "note": "", "params": [0, 0, 0, 12, 12, 12, 0, 0], "price": 12000}, {"id": 134, "atypeId": 0, "description": "A tome that embodies valor, enhancing courage and strength.", "etypeId": 2, "traits": [], "iconIndex": 1555, "name": "<PERSON>or <PERSON>", "note": "", "params": [0, 0, 0, 16, 16, 16, 0, 0], "price": 16000}, {"id": 135, "atypeId": 0, "description": "A chronicle of honor, enhancing noble virtues and integrity.", "etypeId": 2, "traits": [], "iconIndex": 1556, "name": "Honor's Chronicle", "note": "", "params": [0, 0, 0, 20, 20, 20, 0, 0], "price": 20000}, {"id": 136, "atypeId": 0, "description": "A compendium of glory, enhancing heroic deeds and renown.", "etypeId": 2, "traits": [], "iconIndex": 1557, "name": "Glory Compendium", "note": "", "params": [0, 0, 0, 24, 24, 24, 0, 0], "price": 24000}, {"id": 137, "atypeId": 0, "description": "A manuscript of holy texts, enhancing divine magic and faith.", "etypeId": 2, "traits": [], "iconIndex": 1558, "name": "Holy Manuscript", "note": "", "params": [0, 0, 0, 28, 28, 28, 0, 0], "price": 28000}, {"id": 138, "atypeId": 0, "description": "An edict from the heavens, enhancing celestial magic and authority.", "etypeId": 2, "traits": [], "iconIndex": 1559, "name": "Celestial Edict", "note": "", "params": [0, 0, 0, 32, 32, 32, 0, 0], "price": 32000}, {"id": 139, "atypeId": 0, "description": "A ledger of martyrdom, enhancing sacrifice and redemption magic.", "etypeId": 2, "traits": [], "iconIndex": 1560, "name": "Martyr's Ledger", "note": "", "params": [0, 0, 0, 36, 36, 36, 0, 0], "price": 36000}, {"id": 140, "atypeId": 1, "description": "", "etypeId": 4, "traits": [], "iconIndex": 135, "name": "-----<PERSON>", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 141, "atypeId": 6, "description": "Increases defense. Block 5%", "etypeId": 2, "traits": [], "iconIndex": 1591, "name": "Copper Shield", "note": "<block chance: 5%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 2, 0, 1, 0, 0], "price": 300}, {"id": 142, "atypeId": 6, "description": "Increases defense. Block 10%", "etypeId": 2, "traits": [], "iconIndex": 1585, "name": "Bronze Shield", "note": "<block chance: 10%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 4, 0, 2, 0, 0], "price": 800}, {"id": 143, "atypeId": 6, "description": "Increases defense. Block 15%", "etypeId": 2, "traits": [], "iconIndex": 1592, "name": "Silver Shield", "note": "<block chance: 15%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 6, 0, 3, 0, 0], "price": 1500}, {"id": 144, "atypeId": 6, "description": "Increases defense. Block 20%", "etypeId": 2, "traits": [], "iconIndex": 1586, "name": "Golden Shield", "note": "<block chance: 20%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 8, 0, 4, 0, 0], "price": 3000}, {"id": 145, "atypeId": 6, "description": "Increases defense. Block 30%", "etypeId": 2, "traits": [], "iconIndex": 1588, "name": "Platinum Shield", "note": "<block chance: 25%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 24, 0, 12, 0, 0], "price": 4500}, {"id": 146, "atypeId": 5, "description": "A shield crafted with sapphire, reducing water and ice damage by 12%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 3, "value": 0.88}, {"code": 11, "dataId": 5, "value": 0.88}], "iconIndex": 1588, "name": "Sapphire Shield", "note": "<block chance: 20%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 0, 0], "price": 2000}, {"id": 147, "atypeId": 5, "description": "A shield crafted with ruby, reducing fire and lightning damage by 12%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 2, "value": 0.88}, {"code": 11, "dataId": 4, "value": 0.88}], "iconIndex": 1584, "name": "Ruby Shield", "note": "<block chance: 20%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 0, 0], "price": 2000}, {"id": 148, "atypeId": 5, "description": "A shield crafted with emerald, reducing earth and wind damage by 12%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 6, "value": 0.88}, {"code": 11, "dataId": 7, "value": 0.88}], "iconIndex": 1587, "name": "Emerald Shield", "note": "<block chance: 20%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 0, 0], "price": 2000}, {"id": 149, "atypeId": 5, "description": "A shield crafted with amethyst, reducing light and dark damage by 12%.", "etypeId": 2, "traits": [{"code": 11, "dataId": 8, "value": 0.88}, {"code": 11, "dataId": 9, "value": 0.88}], "iconIndex": 1589, "name": "Amethyst Shield", "note": "<block chance: 20%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 16, 0, 0], "price": 2000}, {"id": 150, "atypeId": 6, "description": "Increases defense. Block 30% but -3 agility.", "etypeId": 2, "traits": [], "iconIndex": 2873, "name": "Aegis Shield", "note": "<block chance: 30%>\n<sockets: ALL>", "params": [0, 0, 0, 6, 0, 3, -3, 0], "price": 3000}, {"id": 151, "atypeId": 6, "description": "Increases defense. Block 5% but doubles the damage of Shield Toss.", "etypeId": 2, "traits": [], "iconIndex": 1593, "name": "Battle Shield", "note": "<block chance: 5%>\n<sockets: ALL>", "params": [0, 0, 0, 6, 0, 3, 0, 0], "price": 3000}, {"id": 152, "atypeId": 0, "description": "A buckler worn by sentries, offering vigilant protection.", "etypeId": 2, "traits": [], "iconIndex": 1584, "name": "<PERSON><PERSON>'s Buckler", "note": "", "params": [0, 0, 0, 12, 0, 8, 0, 0], "price": 8000}, {"id": 153, "atypeId": 0, "description": "A ward with a heart of stone, offering unyielding defense.", "etypeId": 2, "traits": [], "iconIndex": 1585, "name": "Stoneheart Ward", "note": "", "params": [0, 0, 0, 16, 0, 12, 0, 0], "price": 12000}, {"id": 154, "atypeId": 0, "description": "A pavise that withstands tempests, offering storm protection.", "etypeId": 2, "traits": [], "iconIndex": 1586, "name": "Tempest Pavise", "note": "", "params": [0, 0, 0, 20, 0, 16, 0, 0], "price": 16000}, {"id": 155, "atypeId": 0, "description": "A guard that stands stalwart, offering unwavering protection.", "etypeId": 2, "traits": [], "iconIndex": 1587, "name": "Stalwart Guard", "note": "", "params": [0, 0, 0, 24, 0, 20, 0, 0], "price": 20000}, {"id": 156, "atypeId": 0, "description": "A targe worn by zealots, offering fanatical protection.", "etypeId": 2, "traits": [], "iconIndex": 1588, "name": "<PERSON><PERSON><PERSON>'s <PERSON><PERSON>", "note": "", "params": [0, 0, 0, 28, 0, 24, 0, 0], "price": 24000}, {"id": 157, "atypeId": 0, "description": "A discus that rises like a phoenix, offering rebirth protection.", "etypeId": 2, "traits": [], "iconIndex": 1589, "name": "<PERSON> Discus", "note": "", "params": [0, 0, 0, 32, 0, 28, 0, 0], "price": 28000}, {"id": 158, "atypeId": 0, "description": "A kite shield crafted from dragon scales, offering legendary protection.", "etypeId": 2, "traits": [], "iconIndex": 1590, "name": "Dragons<PERSON>e", "note": "", "params": [0, 0, 0, 36, 0, 32, 0, 0], "price": 36000}, {"id": 159, "atypeId": 0, "description": "A slab that moves like a juggernaut, offering unstoppable defense.", "etypeId": 2, "traits": [], "iconIndex": 1591, "name": "Juggernaut Slab", "note": "", "params": [0, 0, 0, 40, 0, 36, 0, 0], "price": 40000}, {"id": 160, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "-----<PERSON>", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 161, "atypeId": 0, "description": "Dramatically increases calculation speed, allowing <PERSON> to reduce the cooldowns on all his abilities, enabling faster deployment of gadgets and tech skills.", "etypeId": 2, "traits": [], "iconIndex": 143, "name": "Quantum Processor", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 8, 0, 8, 0, 0], "price": 8000}, {"id": 162, "atypeId": 0, "description": "Activates a swarm of nanobots that passively repair <PERSON>'s armor and slowly regenerate health over time during combat.", "etypeId": 2, "traits": [], "iconIndex": 145, "name": "Nano-Repair Chip", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 12, 0, 12, 0, 0], "price": 12000}, {"id": 163, "atypeId": 0, "description": "Gives <PERSON>'s attacks a chance to emit a small EMP burst, temporarily disabling mechanical enemies or enemy shields.", "etypeId": 2, "traits": [], "iconIndex": 146, "name": "EMP Disruptor", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 16, 0, 16, 0, 0], "price": 16000}, {"id": 164, "atypeId": 0, "description": "Allows <PERSON> to deploy a holographic decoy that distracts enemies, drawing fire away from him and his allies.", "etypeId": 2, "traits": [], "iconIndex": 221, "name": "Holo-Projector", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 20, 0, 20, 0, 0], "price": 20000}, {"id": 165, "atypeId": 0, "description": "Boosts energy output, enhancing the damage of all tech-based attacks or providing an energy shield that absorbs a percentage of incoming damage.", "etypeId": 2, "traits": [], "iconIndex": 159, "name": "Arc Reactor Chip", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 24, 0, 24, 0, 0], "price": 24000}, {"id": 166, "atypeId": 0, "description": "Manipulates gravitational fields to increase <PERSON>'s mobility, granting him enhanced dodge capabilities and movement speed.", "etypeId": 2, "traits": [], "iconIndex": 196, "name": "Gravity Stabilizer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 28, 0, 28, 0, 0], "price": 28000}, {"id": 167, "atypeId": 0, "description": "Enhances <PERSON>'s perception, revealing hidden traps, secret doors, or weaknesses in enemy armor.", "etypeId": 2, "traits": [], "iconIndex": 222, "name": "Spectral Analyzer", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 32, 0, 32, 0, 0], "price": 32000}, {"id": 168, "atypeId": 0, "description": "Projects a light-based shield around <PERSON> when his health drops below a certain threshold, offering a second chance by absorbing a significant amount of damage.", "etypeId": 2, "traits": [], "iconIndex": 2896, "name": "<PERSON><PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 36, 0, 36, 0, 0], "price": 36000}, {"id": 169, "atypeId": 0, "description": "Improves <PERSON>'s synergy with his gadgets, increasing the effectiveness of deployed devices and potentially allowing for dual deployment of certain gadgets.", "etypeId": 2, "traits": [], "iconIndex": 2897, "name": "Cyber-Sync Chip", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 40, 0, 40, 0, 0], "price": 40000}, {"id": 170, "atypeId": 0, "description": "Upgrades <PERSON>'s weaponry to deal additional plasma damage, effective against both organic and mechanical foes, adding a burning effect that damages over time.", "etypeId": 2, "traits": [], "iconIndex": 2918, "name": "Plasma Infusion", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 44, 0, 44, 0, 0], "price": 45000}, {"id": 171, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 172, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 173, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 174, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 175, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 176, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 177, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 178, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 179, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 180, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 181, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 182, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 183, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 184, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 185, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 186, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 187, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 188, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 189, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 190, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 191, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 192, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 193, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 194, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 195, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 196, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 197, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 198, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 199, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 200, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 201, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 202, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 203, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 204, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 205, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 206, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 207, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 208, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 209, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 210, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 211, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 212, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 213, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 214, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 215, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 216, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 217, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 218, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 219, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 220, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 221, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 222, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 223, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 224, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 225, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 226, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 227, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 228, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 229, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 230, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 231, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 232, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 233, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 234, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 235, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 236, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 237, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 238, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 239, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 240, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "-----<PERSON><PERSON>", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 241, "atypeId": 1, "description": "Increases ATK by 15 and PWR by 5 per turn.", "etypeId": 5, "traits": [{"code": 22, "dataId": 9, "value": 0.05}], "iconIndex": 2952, "name": "Warrior's Amulet", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 15, 0, 0, 0, 0, 0], "price": 12000}, {"id": 242, "atypeId": 1, "description": "Increases MAT by 15 and critical chance by 5%", "etypeId": 5, "traits": [{"code": 22, "dataId": 2, "value": 0.05}], "iconIndex": 2968, "name": "<PERSON>'s Talisman", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 15, 0, 0, 0], "price": 12000}, {"id": 243, "atypeId": 1, "description": "Increases DEF by 12, MHP by 100, and RGN by 2%", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.02}], "iconIndex": 2916, "name": "Guardian's Ring", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [100, 0, 0, 12, 0, 0, 0, 0], "price": 15000}, {"id": 244, "atypeId": 1, "description": "Increases ATK and DEF by 8, and PWR charge rate by 20%", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 1.2}], "iconIndex": 2904, "name": "Adventurer's Charm", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 8, 0, 8, 0, 0, 0], "price": 10000}, {"id": 245, "atypeId": 1, "description": "Increases MAT and MDF by 8, and magic evasion by 5%", "etypeId": 5, "traits": [{"code": 22, "dataId": 4, "value": 0.05}], "iconIndex": 2896, "name": "<PERSON><PERSON>'s Brooch", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 10000}, {"id": 246, "atypeId": 1, "description": "Increases DEF and MDF by 8, MHP by 50 and evasion by 5%", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0.05}], "iconIndex": 2945, "name": "Protector's Pendant", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [50, 0, 0, 8, 0, 8, 0, 0], "price": 12000}, {"id": 247, "atypeId": 1, "description": "Increases all stats by 2 and adds a random buff at battle start.", "etypeId": 5, "traits": [], "iconIndex": 3012, "name": "Hero's Medallion", "note": "<sockets: ALL, ALL>\n<rarityEligible>\n<JS Post-Start Battle>\n(() => {\n    // Define possible buffs\n    const buffs = [\n        { id: 2, turns: 5 }, // ATK buff for 5 turns\n        { id: 3, turns: 5 }, // DEF buff for 5 turns\n        { id: 4, turns: 5 }, // MAG buff for 5 turns\n        { id: 5, turns: 5 }, // MDF buff for 5 turns\n        { id: 6, turns: 5 }, // AGI buff for 5 turns\n        { id: 7, turns: 5 }  // LUK buff for 5 turns\n    ];\n\n    // Select a random buff\n    const randomBuff = buffs[Math.floor(Math.random() * buffs.length)];\n\n    // Apply the buff to the user\n    user.addBuff(randomBuff.id, randomBuff.turns);\n})();\n</JS Post-Start Battle>\n\n", "params": [0, 0, 2, 2, 2, 2, 2, 2], "price": 20000}, {"id": 248, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "-----Fishing", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 249, "atypeId": 1, "description": "Increases water resistance and slightly boosts magic defense.", "etypeId": 5, "traits": [{"code": 11, "dataId": 5, "value": 0.85}, {"code": 21, "dataId": 5, "value": 1.15}], "iconIndex": 3024, "name": "Shell Earring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 5000}, {"id": 250, "atypeId": 1, "description": "A ring crafted from seashells, enhancing water resistance and magic defense.", "etypeId": 5, "traits": [{"code": 11, "dataId": 5, "value": 0.8}, {"code": 21, "dataId": 5, "value": 1.2}], "iconIndex": 2992, "name": "Shell Ring", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 10000}, {"id": 251, "atypeId": 1, "description": "A pendant crafted from seashells, providing strong water resistance and magic defense.", "etypeId": 5, "traits": [{"code": 11, "dataId": 5, "value": 0.75}, {"code": 21, "dataId": 5, "value": 1.25}], "iconIndex": 3008, "name": "Shell Pendant", "note": "<sockets: ALL, ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 15000}, {"id": 252, "atypeId": 1, "description": "Adds a chance to inflict bleeding and increases physical attack power.", "etypeId": 5, "traits": [{"code": 21, "dataId": 2, "value": 1.15}, {"code": 32, "dataId": 14, "value": 0.15}], "iconIndex": 3025, "name": "<PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 5000}, {"id": 253, "atypeId": 1, "description": "A ring that channels the ferocity of sharks, enhancing attack power and bleeding effects.", "etypeId": 5, "traits": [{"code": 21, "dataId": 2, "value": 1.2}, {"code": 32, "dataId": 14, "value": 0.2}], "iconIndex": 2993, "name": "Shark Ring", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 10000}, {"id": 254, "atypeId": 1, "description": "A pendant that channels the ferocity of sharks, providing strong attack power and bleeding effects.", "etypeId": 5, "traits": [{"code": 21, "dataId": 2, "value": 1.25}, {"code": 32, "dataId": 14, "value": 0.25}], "iconIndex": 3009, "name": "Shark Pendant", "note": "<sockets: ALL, ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 15000}, {"id": 255, "atypeId": 1, "description": "Increases earth resistance and physical defense.", "etypeId": 5, "traits": [{"code": 11, "dataId": 6, "value": 0.85}, {"code": 21, "dataId": 3, "value": 1.15}], "iconIndex": 3026, "name": "<PERSON><PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 5000}, {"id": 256, "atypeId": 1, "description": "A ring crafted from ancient wood, enhancing earth resistance and physical defense.", "etypeId": 5, "traits": [{"code": 11, "dataId": 6, "value": 0.8}, {"code": 21, "dataId": 3, "value": 1.2}], "iconIndex": 2994, "name": "Wooden Ring", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 10000}, {"id": 257, "atypeId": 1, "description": "A pendant crafted from ancient wood, providing strong earth resistance and physical defense.", "etypeId": 5, "traits": [{"code": 11, "dataId": 6, "value": 0.75}, {"code": 21, "dataId": 3, "value": 1.25}], "iconIndex": 3010, "name": "<PERSON><PERSON>", "note": "<sockets: ALL, ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 15000}, {"id": 258, "atypeId": 1, "description": "Increases luck and slightly boosts health regeneration.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.05}, {"code": 21, "dataId": 7, "value": 1.15}], "iconIndex": 3032, "name": "<PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 5000}, {"id": 259, "atypeId": 1, "description": "A ring adorned with pearls, enhancing luck and health regeneration.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}, {"code": 21, "dataId": 7, "value": 1.2}], "iconIndex": 3000, "name": "<PERSON>", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 10000}, {"id": 260, "atypeId": 1, "description": "A pendant adorned with pearls, providing strong luck and health regeneration.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.15}, {"code": 21, "dataId": 7, "value": 1.25}], "iconIndex": 3016, "name": "Pearl <PERSON>", "note": "<sockets: ALL, ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 15000}, {"id": 261, "atypeId": 1, "description": "Increases wind resistance and slightly boosts magic attack.", "etypeId": 5, "traits": [{"code": 11, "dataId": 7, "value": 0.85}, {"code": 21, "dataId": 4, "value": 1.15}], "iconIndex": 3033, "name": "<PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 5000}, {"id": 262, "atypeId": 1, "description": "Increases wind resistance and slightly boosts magic attack.", "etypeId": 5, "traits": [{"code": 11, "dataId": 7, "value": 0.8}, {"code": 21, "dataId": 4, "value": 1.2}], "iconIndex": 3001, "name": "Crystal Ring", "note": "<sockets: ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 10000}, {"id": 263, "atypeId": 1, "description": "A pendant crafted from crystal, providing strong wind resistance and magic attack.", "etypeId": 5, "traits": [{"code": 11, "dataId": 7, "value": 0.75}, {"code": 21, "dataId": 4, "value": 1.25}], "iconIndex": 3017, "name": "Crystal Pendant", "note": "<sockets: ALL, ALL, ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 15000}, {"id": 264, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 2945, "name": "-----Accessories", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 265, "atypeId": 1, "description": "A charm that increases Defense.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.05}], "iconIndex": 145, "name": "Sturdy Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 5, 0, 5, 0, 0], "price": 3000}, {"id": 266, "atypeId": 1, "description": "A ring that increases Defense.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 146, "name": "<PERSON><PERSON><PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 10, 0, 10, 0, 0], "price": 1000}, {"id": 267, "atypeId": 1, "description": "<ColorLock>A charm that increases Attack.</ColorLock>", "etypeId": 5, "traits": [{"code": 22, "dataId": 2, "value": 0.05}], "iconIndex": 145, "name": "Mighty Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 5, 0, 5, 0, 0, 0], "price": 3000}, {"id": 268, "atypeId": 1, "description": "<ColorLock>A pendant that increases Attack.</ColorLock>", "etypeId": 5, "traits": [{"code": 22, "dataId": 2, "value": 0.1}], "iconIndex": 146, "name": "Mighty Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 10, 0, 10, 0, 0, 0], "price": 1000}, {"id": 269, "atypeId": 1, "description": "A charm that increases Agility.", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0.05}], "iconIndex": 145, "name": "Swift Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 10, 0], "price": 3000}, {"id": 270, "atypeId": 1, "description": "A ring that increases Agility.", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0.1}], "iconIndex": 146, "name": "Swift Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 20, 0], "price": 1000}, {"id": 271, "atypeId": 1, "description": "A charm that increases Luck.", "etypeId": 5, "traits": [{"code": 23, "dataId": 9, "value": 1.05}], "iconIndex": 145, "name": "Fortune Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 10], "price": 3000}, {"id": 272, "atypeId": 1, "description": "A ring that increases Luck.", "etypeId": 5, "traits": [{"code": 23, "dataId": 9, "value": 1.1}], "iconIndex": 146, "name": "Fortune Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 20], "price": 1000}, {"id": 273, "atypeId": 1, "description": "<ColorLock>A charm that increases M.Attack.</ColorLock>", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 145, "name": "Mystic Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 3000}, {"id": 274, "atypeId": 1, "description": "<ColorLock>A ring that increases M.Attack.</ColorLock>", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.2}], "iconIndex": 146, "name": "Mystic Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 10, 10, 0, 0], "price": 1000}, {"id": 275, "atypeId": 1, "description": "Protect allies with low health.", "etypeId": 5, "traits": [{"code": 62, "dataId": 2, "value": 1}], "iconIndex": 145, "name": "Protect Ring", "note": "<sockets: ALL>\n", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 276, "atypeId": 1, "description": "Restores 10% health per turn.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 145, "name": "<PERSON>w <PERSON>", "note": "<sockets: ALL>\n", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 277, "atypeId": 1, "description": "Restores 1 Tech Point per turn.", "etypeId": 5, "traits": [{"code": 22, "dataId": 8, "value": 0.2}], "iconIndex": 145, "name": "Tech Ring", "note": "<sockets: ALL>\n", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 278, "atypeId": 1, "description": "Increases maximum health by 10%", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.05}], "iconIndex": 145, "name": "Vital Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [10, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 279, "atypeId": 1, "description": "A pendant that significantly increases maximum health and vitality.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 146, "name": "<PERSON>l Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [20, 0, 0, 0, 0, 0, 0, 0], "price": 8000}, {"id": 280, "atypeId": 1, "description": "An orb that helps prevent Poison and Paralysis.", "etypeId": 5, "traits": [{"code": 13, "dataId": 4, "value": 0.6}, {"code": 13, "dataId": 12, "value": 0.6}], "iconIndex": 165, "name": "Poison Guard", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 3000}, {"id": 281, "atypeId": 1, "description": "An orb that entirely prevents Poison, <PERSON>lee<PERSON> and Paralysis states.", "etypeId": 5, "traits": [{"code": 14, "dataId": 4, "value": 1}, {"code": 14, "dataId": 12, "value": 1}, {"code": 14, "dataId": 17, "value": 1}], "iconIndex": 165, "name": "Super Poison Guard", "note": "", "params": [0, 0, 0, 5, 0, 0, 0, 0], "price": 3000}, {"id": 282, "atypeId": 0, "description": "Increases party experience gain by 15% per stack.", "etypeId": 6, "traits": [{"code": 23, "dataId": 9, "value": 1.15}], "iconIndex": 267, "name": "Experience Egg", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 283, "atypeId": 1, "description": "A charm that has a 30% chance to prevent Sleep, Silence, Confusion, and Charm.", "etypeId": 5, "traits": [{"code": 13, "dataId": 6, "value": 0.3}, {"code": 13, "dataId": 9, "value": 0.3}, {"code": 13, "dataId": 10, "value": 0.3}, {"code": 13, "dataId": 8, "value": 0.3}], "iconIndex": 160, "name": "Safety Crystal", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 284, "atypeId": 1, "description": "[Accessory] A ring that entirely prevents Sleep,\nSilence, Confusion, Charm, and Rage states.", "etypeId": 5, "traits": [{"code": 14, "dataId": 6, "value": 1}, {"code": 14, "dataId": 8, "value": 1}, {"code": 14, "dataId": 7, "value": 1}, {"code": 14, "dataId": 9, "value": 1}, {"code": 14, "dataId": 10, "value": 1}], "iconIndex": 160, "name": "Super Mental Guard", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 6000}, {"id": 285, "atypeId": 1, "description": "Increases Wind damage by 30%, boosts Agility by 20%,\nand raises Evasion by 10%", "etypeId": 5, "traits": [{"code": 21, "dataId": 6, "value": 1.2}, {"code": 22, "dataId": 1, "value": 0.1}], "iconIndex": 2052, "name": "Tempest Gem", "note": "<Dealt Element Wind Rate: 130%>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 5000}, {"id": 286, "atypeId": 1, "description": "Luck lowered by 90%, but have 20% chance on being hit to cast Crushing Nightmare.", "etypeId": 5, "traits": [{"code": 21, "dataId": 7, "value": 0.1}, {"code": 43, "dataId": 183, "value": 1}], "iconIndex": 3065, "name": "Cursed Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 287, "atypeId": 1, "description": "Token with a 30% chance to cause Bleeding.", "etypeId": 5, "traits": [{"code": 32, "dataId": 14, "value": 0.3}], "iconIndex": 294, "name": "Werewolf Fang", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 288, "atypeId": 1, "description": "Token with a 20% chance to cast Spore on all enemies when attacked.", "etypeId": 5, "traits": [{"code": 43, "dataId": 184, "value": 1}], "iconIndex": 2346, "name": "Noxious Spore", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 289, "atypeId": 1, "description": "<PERSON><PERSON> with a 40% chance to cast <PERSON><PERSON> on attackers.", "etypeId": 5, "traits": [{"code": 43, "dataId": 185, "value": 1}], "iconIndex": 2345, "name": "Mandrake Root", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 290, "atypeId": 1, "description": "Ring that increases Attack and Defense by 20%", "etypeId": 5, "traits": [{"code": 22, "dataId": 2, "value": 0.1}], "iconIndex": 3064, "name": "General's Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 8, 8, 0, 0, 0, 0], "price": 3000}, {"id": 291, "atypeId": 1, "description": "Damage inflicted by Fire, Water, Wind, and Earth is reduced by 25%.", "etypeId": 5, "traits": [{"code": 11, "dataId": 2, "value": 0.25}, {"code": 11, "dataId": 3, "value": 0.25}, {"code": 11, "dataId": 4, "value": 0.25}, {"code": 11, "dataId": 5, "value": 0.25}, {"code": 11, "dataId": 6, "value": 0.25}, {"code": 11, "dataId": 7, "value": 0.25}], "iconIndex": 163, "name": "Elemental Ward", "note": "<Item Ancient Book Buy Cost: 2>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 12000}, {"id": 292, "atypeId": 1, "description": "Ring with a 20% chance to cast Fire on attackers.", "etypeId": 5, "traits": [{"code": 43, "dataId": 187, "value": 1}], "iconIndex": 3056, "name": "Backfire Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 293, "atypeId": 1, "description": "<PERSON> with a 20% chance to cast <PERSON><PERSON><PERSON> on attackers.", "etypeId": 5, "traits": [{"code": 43, "dataId": 186, "value": 1}], "iconIndex": 3057, "name": "Frostbite Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 294, "atypeId": 1, "description": "20% resistance to Water damage and 50% resistance to Soaking debuff.", "etypeId": 5, "traits": [{"code": 11, "dataId": 5, "value": 0.8}, {"code": 13, "dataId": 38, "value": 0.5}], "iconIndex": 2949, "name": "Seafoam Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 295, "atypeId": 1, "description": "20% resistance to Wind damage and 50% resistance to Disoriented debuff.", "etypeId": 5, "traits": [{"code": 11, "dataId": 7, "value": 0.8}, {"code": 13, "dataId": 40, "value": 0.5}], "iconIndex": 2950, "name": "<PERSON><PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 296, "atypeId": 1, "description": "Increases Light damage by 15%", "etypeId": 5, "traits": [], "iconIndex": 2968, "name": "Light Earring", "note": "<Dealt Element 9 Plus: +15%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 297, "atypeId": 1, "description": "20% resistance to Dark damage and 50% resistance to Cursed debuff.", "etypeId": 5, "traits": [{"code": 11, "dataId": 9, "value": 0.8}, {"code": 13, "dataId": 41, "value": 0.5}], "iconIndex": 2969, "name": "<PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 298, "atypeId": 1, "description": "Increases Fire damage by 15%", "etypeId": 5, "traits": [], "iconIndex": 2962, "name": "Fire Earring", "note": "<Dealt Element Fire Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 299, "atypeId": 1, "description": "Increases Ice damage by 15%", "etypeId": 5, "traits": [], "iconIndex": 2963, "name": "Ice Earring", "note": "<Dealt Element Ice Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 300, "atypeId": 1, "description": "Increases Thunder damage by 15%", "etypeId": 5, "traits": [], "iconIndex": 2964, "name": "Thunder Earring", "note": "<Dealt Element Thunder Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 301, "atypeId": 1, "description": "Increases Water damage by 15%", "etypeId": 5, "traits": [], "iconIndex": 2965, "name": "Water Earring", "note": "<Dealt Element Water Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 302, "atypeId": 1, "description": "Increases Earth damage by 15%", "etypeId": 5, "traits": [], "iconIndex": 2967, "name": "Earth Earring", "note": "<Dealt Element Earth Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 303, "atypeId": 1, "description": "Increases Wind damage by 15%", "etypeId": 5, "traits": [], "iconIndex": 2966, "name": "Wind Earring", "note": "<Dealt Element Wind Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 304, "atypeId": 1, "description": "Instead of dying, the user regenerates 1/3 of their max health.\n10 turn cooldown.", "etypeId": 5, "traits": [{"code": 43, "dataId": 189, "value": 1}], "iconIndex": 221, "name": "<PERSON>", "note": "<Item Ancient Book Buy Cost: 1>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 305, "atypeId": 1, "description": "Gives a 20% chance to retaliate against normal attacks on your party.", "etypeId": 5, "traits": [{"code": 43, "dataId": 188, "value": 1}], "iconIndex": 159, "name": "Revenge Ring", "note": "<Item Ancient Book Buy Cost: 1>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 306, "atypeId": 0, "description": "A hairpin that enhances magic power and mystical abilities.", "etypeId": 2, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2896, "name": "Mystic Hairpin", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 8, 5, 0, 0], "price": 8000}, {"id": 307, "atypeId": 0, "description": "A mysterious key that can unlock hidden passages and secrets.", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0.05}], "iconIndex": 2897, "name": "Skeleton Key", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 5, 8], "price": 5000}, {"id": 308, "atypeId": 0, "description": "Attacks have a 30% chance to poison.", "etypeId": 2, "traits": [{"code": 32, "dataId": 4, "value": 0.3}], "iconIndex": 2918, "name": "Serpent Ring", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 4000}, {"id": 309, "atypeId": 0, "description": "Increases luck by 30%", "etypeId": 2, "traits": [{"code": 23, "dataId": 9, "value": 1.1}], "iconIndex": 2901, "name": "Gold Clover", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 15], "price": 12000}, {"id": 310, "atypeId": 0, "description": "Increases gold found in battles.", "etypeId": 2, "traits": [{"code": 21, "dataId": 7, "value": 1.1}, {"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 2902, "name": "Bronze Coin", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 311, "atypeId": 0, "description": "Increases critical hit rate.", "etypeId": 2, "traits": [{"code": 22, "dataId": 2, "value": 0.1}], "iconIndex": 2903, "name": "Ace of Spades", "note": "<rarityEligible>", "params": [0, 0, 5, 0, 0, 0, 0, 0], "price": 5000}, {"id": 312, "atypeId": 0, "description": "Increase experience gain.", "etypeId": 2, "traits": [{"code": 23, "dataId": 9, "value": 1.1}], "iconIndex": 2904, "name": "<PERSON>'s Foot", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 5, 0], "price": 4000}, {"id": 313, "atypeId": 0, "description": "Increases chance of finding items on enemies.", "etypeId": 2, "traits": [{"code": 21, "dataId": 7, "value": 1.15}, {"code": 22, "dataId": 7, "value": 0.15}], "iconIndex": 2905, "name": "Lucky Cat", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 8000}, {"id": 314, "atypeId": 0, "description": "Small increase to defense.", "etypeId": 2, "traits": [{"code": 22, "dataId": 7, "value": 0.05}], "iconIndex": 2912, "name": "Silver Loop", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 3, 0, 3, 0, 0], "price": 2000}, {"id": 315, "atypeId": 0, "description": "Boosts reputation with NPCs.", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0.05}], "iconIndex": 2913, "name": "Royal Signet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 5, 10], "price": 6000}, {"id": 316, "atypeId": 0, "description": "Adds necrotic damage to attacks.", "etypeId": 2, "traits": [{"code": 22, "dataId": 2, "value": 0.05}], "iconIndex": 2915, "name": "<PERSON><PERSON> Signet", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 0, 0, 5], "price": 7000}, {"id": 317, "atypeId": 0, "description": "A ring that enhances warrior abilities and combat prowess.", "etypeId": 2, "traits": [{"code": 22, "dataId": 2, "value": 0.1}], "iconIndex": 2916, "name": "Warrior's Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 8, 5, 0, 0, 0, 0], "price": 5000}, {"id": 318, "atypeId": 0, "description": "A ring crafted from redstone, enhancing magical and technological abilities.", "etypeId": 2, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2917, "name": "Redstone Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 10, 0, 0, 0], "price": 10000}, {"id": 319, "atypeId": 0, "description": "A pendant crafted from redstone, enhancing magical and technological abilities.", "etypeId": 2, "traits": [{"code": 23, "dataId": 5, "value": 0.15}], "iconIndex": 2898, "name": "Redstone Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 12, 0, 0, 0], "price": 15000}, {"id": 320, "atypeId": 0, "description": "Grants +15% Luck and Healing.", "etypeId": 2, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 221, "name": "Beacon of Hope", "note": "<Dealt Element 10 Plus: +15%>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 8, 0, 0], "price": 8000}, {"id": 321, "atypeId": 0, "description": "A token that grants stealth abilities and reduces detection.", "etypeId": 2, "traits": [{"code": 22, "dataId": 2, "value": 0.05}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 32, "dataId": 6, "value": 0.03}], "iconIndex": 196, "name": "Silencer's Token", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 5, 0], "price": 6000}, {"id": 322, "atypeId": 0, "description": "A magnifying glass that reveals hidden details and secrets.", "etypeId": 2, "traits": [{"code": 64, "dataId": 3, "value": 1}], "iconIndex": 222, "name": "Magnifying Glass", "note": "<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 3000}, {"id": 323, "atypeId": 0, "description": "A pendant crafted from drake scales, providing dragon-like protection.", "etypeId": 2, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 2899, "name": "Drakescale Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [15, 0, 0, 10, 0, 0, 0, 0], "price": 12000}, {"id": 324, "atypeId": 1, "description": "Increases Light damage by 15% and grants Lumina while worn.", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2936, "name": "Light Ring", "note": "<Dealt Element 8 Plus: +15%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 6000}, {"id": 325, "atypeId": 1, "description": "Increases Dark damage by 15% and grants Shadow while worn.", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2937, "name": "Shade <PERSON>", "note": "<Dealt Element 9 Plus: +20%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 6, 6, 0, 0], "price": 6000}, {"id": 326, "atypeId": 1, "description": "Increases Fire damage by 15% and grants Cinder while worn.", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2930, "name": "Fire Ring", "note": "<Dealt Element Fire Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 6000}, {"id": 327, "atypeId": 1, "description": "Increases Ice damage by 15% and grants I<PERSON>cle while worn.", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2931, "name": "Ice Ring", "note": "<Dealt Element Ice Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 6000}, {"id": 328, "atypeId": 1, "description": "Increases Thunder damage by 15% and grants Static while worn.", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2932, "name": "Thunder Ring", "note": "<Dealt Element Thunder Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 6000}, {"id": 329, "atypeId": 1, "description": "Increases Water damage by 15% and grants Splash while worn.", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2933, "name": "Water Ring", "note": "<Dealt Element Water Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 6000}, {"id": 330, "atypeId": 1, "description": "Increases Earth damage by 15% and grants Fissure while worn.", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2935, "name": "Earth Ring", "note": "<Dealt Element Earth Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 6000}, {"id": 331, "atypeId": 1, "description": "Increases Wind damage by 15% and grants <PERSON><PERSON> while worn.", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.1}], "iconIndex": 2934, "name": "Wind Ring", "note": "<Dealt Element Wind Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 6000}, {"id": 332, "atypeId": 1, "description": "Increases Light damage by 15%", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.15}], "iconIndex": 2952, "name": "Light Pendant", "note": "<Dealt Element 9 Plus: +15%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 6000}, {"id": 333, "atypeId": 1, "description": "20% resistance to Dark damage and 50% resistance to Cursed debuff.", "etypeId": 5, "traits": [{"code": 11, "dataId": 9, "value": 0.8}, {"code": 13, "dataId": 41, "value": 0.5}], "iconIndex": 2953, "name": "Shade Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 6000}, {"id": 334, "atypeId": 1, "description": "Increases Fire damage by 15%", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.15}], "iconIndex": 2946, "name": "Fire Pendant", "note": "<Dealt Element Fire Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 6000}, {"id": 335, "atypeId": 1, "description": "Increases Ice damage by 15%", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.15}], "iconIndex": 2947, "name": "Ice Pendant", "note": "<Dealt Element Ice Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 6000}, {"id": 336, "atypeId": 1, "description": "Increases Thunder damage by 15%", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.15}], "iconIndex": 2948, "name": "Thunder Pendant", "note": "<Dealt Element Thunder Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 6000}, {"id": 337, "atypeId": 1, "description": "Increases Water damage by 15%", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.15}], "iconIndex": 2949, "name": "Water Pendant", "note": "<Dealt Element Water Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 6000}, {"id": 338, "atypeId": 1, "description": "Increases Earth damage by 15%", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.15}], "iconIndex": 2951, "name": "Earth Pendant", "note": "<Dealt Element Earth Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 6000}, {"id": 339, "atypeId": 1, "description": "Increases Wind damage by 15%", "etypeId": 5, "traits": [{"code": 23, "dataId": 5, "value": 0.15}], "iconIndex": 2950, "name": "Wind Pendant", "note": "<Dealt Element Wind Rate: 115%>\n<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 6000}, {"id": 340, "atypeId": 1, "description": "An earring blessed by <PERSON><PERSON>, continuously renewing health.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.05}], "iconIndex": 2960, "name": "Gaia's <PERSON><PERSON><PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [10, 0, 0, 0, 0, 0, 0, 0], "price": 8000}, {"id": 341, "atypeId": 1, "description": "A ring blessed by <PERSON><PERSON>, enhancing earth magic and natural abilities.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 2960, "name": "Gaia's Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [15, 0, 0, 0, 0, 0, 5, 0], "price": 12000}, {"id": 342, "atypeId": 1, "description": "A pendant blessed by <PERSON><PERSON>, enhancing earth magic and natural abilities.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.15}], "iconIndex": 2960, "name": "Gaia's Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [20, 0, 0, 0, 0, 0, 8, 0], "price": 15000}, {"id": 343, "atypeId": 1, "description": "An earring that channels crimson energy, stealing health from enemies.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.05}], "iconIndex": 2961, "name": "<PERSON>", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 5, 0, 0, 5], "price": 8000}, {"id": 344, "atypeId": 1, "description": "A ring that channels crimson energy, enhancing dark magic and vampiric abilities.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 2929, "name": "Crimson Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 8, 0, 0, 8], "price": 12000}, {"id": 345, "atypeId": 1, "description": "A pendant that channels crimson energy, enhancing dark magic and vampiric abilities.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.15}], "iconIndex": 2945, "name": "Crimson Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 0, 10, 0, 0, 10], "price": 15000}, {"id": 346, "atypeId": 1, "description": "Increases physical defense and slightly boosts PWR recovery.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.05}], "iconIndex": 3027, "name": "Copper Earring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 3, 0, 0, 0, 0], "price": 3000}, {"id": 347, "atypeId": 1, "description": "A ring crafted from copper, enhancing physical defense and power recovery.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 2995, "name": "Copper Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 5, 0, 0, 0, 0], "price": 5000}, {"id": 348, "atypeId": 1, "description": "A pendant crafted from copper, enhancing physical defense and power recovery.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.15}], "iconIndex": 3011, "name": "Copper Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 8, 0, 0, 0, 0], "price": 7000}, {"id": 349, "atypeId": 1, "description": "Boosts physical defense and grants a minor health shield at the start of battles.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.05}], "iconIndex": 3028, "name": "Bronze Earring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [5, 0, 0, 4, 0, 0, 0, 0], "price": 4000}, {"id": 350, "atypeId": 1, "description": "A ring crafted from bronze, providing enhanced physical defense and battle readiness.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 2996, "name": "Bronze Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [10, 0, 0, 6, 0, 0, 0, 0], "price": 6000}, {"id": 351, "atypeId": 1, "description": "A pendant crafted from bronze, providing enhanced physical defense and battle readiness.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.15}], "iconIndex": 3012, "name": "Bronze Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [15, 0, 0, 8, 0, 0, 0, 0], "price": 8000}, {"id": 352, "atypeId": 1, "description": "Enhances physical defense and slightly increases evasion rate and poison resist.", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0.05}, {"code": 13, "dataId": 4, "value": 0.8}], "iconIndex": 3030, "name": "Silver Earring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 6, 0, 0, 0, 0], "price": 6000}, {"id": 353, "atypeId": 1, "description": "A ring crafted from silver, enhancing physical defense and providing magical protection.", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0.1}], "iconIndex": 2998, "name": "Silver Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 8, 0, 5, 0, 0], "price": 8000}, {"id": 354, "atypeId": 1, "description": "A pendant crafted from silver, enhancing physical defense and providing magical protection.", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0.15}], "iconIndex": 3014, "name": "Silver Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 10, 0, 8, 0, 0], "price": 10000}, {"id": 355, "atypeId": 1, "description": "Greatly boosts physical defense, increases gold earned from battles, and enhances item drop rate.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.2}], "iconIndex": 3031, "name": "Gold Earring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 10, 0, 0, 0, 5], "price": 12000}, {"id": 356, "atypeId": 1, "description": "A ring crafted from gold, providing exceptional physical defense and prosperity.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.25}], "iconIndex": 2999, "name": "Gold Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 12, 0, 0, 0, 8], "price": 15000}, {"id": 357, "atypeId": 1, "description": "A pendant crafted from gold, providing exceptional physical defense and prosperity.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.3}], "iconIndex": 3031, "name": "Gold Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 15, 0, 0, 0, 10], "price": 18000}, {"id": 358, "atypeId": 1, "description": "Maximally boosts physical defense, significantly increases health regeneration, and grants a small chance to negate damage.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.1}, {"code": 22, "dataId": 1, "value": 0.05}], "iconIndex": 3029, "name": "Platinum Earring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 12, 0, 0, 0, 0], "price": 15000}, {"id": 359, "atypeId": 1, "description": "A ring crafted from platinum, providing ultimate physical defense and regeneration.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.15}, {"code": 22, "dataId": 1, "value": 0.1}], "iconIndex": 2997, "name": "Platinum Ring", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 15, 0, 0, 0, 0], "price": 20000}, {"id": 360, "atypeId": 1, "description": "A pendant crafted from platinum, providing ultimate physical defense and regeneration.", "etypeId": 5, "traits": [{"code": 22, "dataId": 7, "value": 0.2}, {"code": 22, "dataId": 1, "value": 0.15}], "iconIndex": 3013, "name": "Platinum Pendant", "note": "<sockets: ALL>\n<rarityEligible>", "params": [0, 0, 0, 18, 0, 0, 0, 0], "price": 25000}, {"id": 361, "atypeId": 1, "description": "Prevents the Chain Gauge from resetting when hit.", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0.05}], "iconIndex": 2916, "name": "Chainguard Ring", "note": "<chainGuard:100>\n<rarityEligible>", "params": [0, 0, 0, 5, 0, 0, 0, 0], "price": 8000}, {"id": 362, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 363, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 364, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "-----Artifacts", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 365, "atypeId": 1, "description": "Restores +1% per stack health to the entire party each turn.", "etypeId": 6, "traits": [{"code": 22, "dataId": 7, "value": 0.01}], "iconIndex": 2097, "name": "Plant Fossil", "note": "<Stackable Party Artifact>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 366, "atypeId": 1, "description": "Grants +2 per stack defense to all party members.", "etypeId": 6, "traits": [], "iconIndex": 2098, "name": "Shell Fossil", "note": "<Stackable Party Artifact>\n<rarityEligible>", "params": [0, 0, 0, 2, 0, 0, 0, 0], "price": 2000}, {"id": 367, "atypeId": 1, "description": "Grants +2 per stack attack to all party members.", "etypeId": 6, "traits": [], "iconIndex": 2099, "name": "Fish Fossil", "note": "<Stackable Party Artifact>\n<rarityEligible>", "params": [0, 0, 2, 0, 0, 0, 0, 0], "price": 2000}, {"id": 368, "atypeId": 1, "description": "Grants +2 per stack magic defense to all party members.", "etypeId": 6, "traits": [], "iconIndex": 2100, "name": "Trilobyte Fossil", "note": "<Stackable Party Artifact>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 2, 0, 2], "price": 2000}, {"id": 369, "atypeId": 0, "description": "Grants +1 Agility and +1 Luck per stack to all party members.", "etypeId": 6, "traits": [], "iconIndex": 199, "name": "<PERSON><PERSON>' <PERSON>rp", "note": "<Stackable Party Artifact>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 1, 1], "price": 2000}, {"id": 370, "atypeId": 0, "description": "The party's attacks have a 1% chance per stack to cast Cowardice.", "etypeId": 6, "traits": [{"code": 32, "dataId": 9, "value": 0.01}], "iconIndex": 200, "name": "Lute of Enchantment", "note": "<Stackable Party Artifact>\n<rarityEligible>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 371, "atypeId": 0, "description": "Grants +2 magic attack per stack to all party members.", "etypeId": 6, "traits": [], "iconIndex": 201, "name": "Mystic Ocarina", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 2, 0, 0, 0], "price": 2000}, {"id": 372, "atypeId": 0, "description": "Increases party's max HP by 50 per stack.", "etypeId": 6, "traits": [], "iconIndex": 203, "name": "Horn of Bjorn", "note": "<Stackable Party Artifact>", "params": [50, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 373, "atypeId": 0, "description": "The party's attacks have a 1% chance per stack to inflict Poison.", "etypeId": 6, "traits": [{"code": 32, "dataId": 4, "value": 0.01}], "iconIndex": 276, "name": "Poison Ivy", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 374, "atypeId": 0, "description": "The party gains 1% per stack resistance to Fire.", "etypeId": 6, "traits": [{"code": 11, "dataId": 2, "value": 0.99}], "iconIndex": 1009, "name": "Ruby Tablet", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 375, "atypeId": 0, "description": "The party gains 1% per stack resistance to Thunder.", "etypeId": 6, "traits": [{"code": 11, "dataId": 4, "value": 0.99}], "iconIndex": 1011, "name": "<PERSON><PERSON><PERSON>", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 376, "atypeId": 0, "description": "The party gains 1% per stack resistance to Wind.", "etypeId": 6, "traits": [{"code": 11, "dataId": 7, "value": 0.99}], "iconIndex": 1013, "name": "Emerald Tablet", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 377, "atypeId": 0, "description": "The party gains 1% per stack resistance to Ice.", "etypeId": 6, "traits": [{"code": 11, "dataId": 3, "value": 0.99}], "iconIndex": 1015, "name": "Sapphire Tablet", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 378, "atypeId": 0, "description": "The party gains 1% per stack resistance to Water.", "etypeId": 6, "traits": [{"code": 11, "dataId": 5, "value": 0.99}], "iconIndex": 1017, "name": "Aquamarine Tablet", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 379, "atypeId": 0, "description": "The party gains 1% per stack resistance to Earth.", "etypeId": 6, "traits": [{"code": 11, "dataId": 6, "value": 0.99}], "iconIndex": 1010, "name": "Geode Tablet", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 380, "atypeId": 0, "description": "The party gains 1% per stack resistance to Light.", "etypeId": 6, "traits": [{"code": 11, "dataId": 8, "value": 0.99}], "iconIndex": 1008, "name": "Opal Tablet", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 381, "atypeId": 0, "description": "The party gains 1% per stack resistance to Darkness.", "etypeId": 6, "traits": [{"code": 11, "dataId": 9, "value": 0.99}], "iconIndex": 1018, "name": "Amethyst Tablet", "note": "<Stackable Party Artifact>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 382, "atypeId": 1, "description": "Where did this come from?", "etypeId": 6, "traits": [{"code": 32, "dataId": 11, "value": 0.01}], "iconIndex": 3105, "name": "Nightmare Brooch", "note": "<Stackable Party Artifact>\n", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 383, "atypeId": 1, "description": "Where did this come from?", "etypeId": 6, "traits": [{"code": 11, "dataId": 10, "value": 1.1}], "iconIndex": 147, "name": "Dream Brooch", "note": "<Stackable Party Artifact>\n", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 384, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 385, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 386, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 387, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 388, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 389, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 390, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 391, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 392, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 393, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 394, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 395, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 396, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 397, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 398, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 399, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 400, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 401, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 402, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 403, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 404, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 405, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 406, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 407, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 408, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 409, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 410, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 411, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 412, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 413, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 414, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 415, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 416, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 417, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 418, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 419, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 420, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 421, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 422, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 423, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 424, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 425, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 426, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 427, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 428, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 429, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 430, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 431, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 432, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 433, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 434, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 435, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 436, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 437, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 438, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 439, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 440, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 441, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 442, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 443, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 444, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 445, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 446, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 447, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 448, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 449, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 450, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 451, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 452, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 453, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 454, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 455, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 456, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 457, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 458, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 459, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 460, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 461, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 462, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 463, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 464, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 465, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 466, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 467, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 468, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 469, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 470, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 471, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 472, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 473, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 474, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 475, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 476, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 477, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 478, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 479, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 480, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 481, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 482, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 483, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 484, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 485, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 486, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 487, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 488, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 489, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 490, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 491, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 492, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 493, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 494, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 495, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 496, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 497, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 498, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 499, "atypeId": 0, "description": "", "etypeId": 2, "traits": [], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 500, "atypeId": 1, "description": "", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0.25}, {"code": 22, "dataId": 8, "value": 1}, {"code": 22, "dataId": 9, "value": 0.6}, {"code": 21, "dataId": 6, "value": 9.99}], "iconIndex": 3641, "name": "Super Mega Disk", "note": "", "params": [5000, 0, 0, 99, 0, 99, 0, 0], "price": 0}]