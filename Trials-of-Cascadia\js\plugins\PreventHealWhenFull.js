/*:
 * @plugindesc Prevent Healing When Full (HP, MP, TP)
 *
 * @help Prevents using healing items/spells on a character when HP, MP, or TP is full (outside battle).
 */

(function () {
    var _Game_Action_testApply = Game_Action.prototype.testApply;

    Game_Action.prototype.testApply = function (target) {
        // Only check outside of battle
        if (!(SceneManager._scene instanceof Scene_Battle)) {
            // HP
            if (this.isRecover() && this.isHpRecover() && target.hp === target.mhp) {
                return false;
            }
            // MP
            if (this.isRecover() && this.isMpRecover() && target.mp === target.mmp) {
                return false;
            }
            // TP (100 is max)
            if (this.isRecover() && this.isTpRecover && this.isTpRecover() && target.tp === 100) {
                return false;
            }
            // Also check for items that just "Gain TP"
            if (this.item() && this.item().effects) {
                for (let effect of this.item().effects) {
                    if (
                        effect.code === Game_Action.EFFECT_GAIN_TP &&
                        target.tp === 100 &&
                        effect.value1 > 0
                    ) {
                        return false;
                    }
                }
            }
        }
        return _Game_Action_testApply.call(this, target);
    };
})();
