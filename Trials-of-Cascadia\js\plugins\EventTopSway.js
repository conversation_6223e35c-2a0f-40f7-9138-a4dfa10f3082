/*:
 * @plugindesc Causes designated events to sway, simulating wind or gentle movement.
 * @version 1.0.0
 * @target MZ
 * @help
 * EventTopSway.js
 * Version 1.0.0
 *
 * This plugin allows you to make events (like trees, bushes, etc.) sway
 * gently, giving a more dynamic feel to your maps.
 *
 * --- How to Use ---
 * To make an event sway, add one of the following notetags to the event's
 * note box (on the relevant event page):
 *
 * 1. <sway_top>
 *    This will make the event sway with the default speed and angle defined
 *    in the plugin parameters.
 *
 * 2. <sway_top: speed_factor max_angle_degrees>
 *    This allows you to specify custom parameters for this particular event.
 *    - speed_factor: A number that multiplies the base sway speed.
 *      (e.g., 1.0 is normal, 0.5 is slower, 2.0 is faster).
 *    - max_angle_degrees: The maximum angle (in degrees) the event will
 *      tilt to either side. (e.g., 5 means it sways between -5 and +5 degrees).
 *
 * Example Notetags:
 *   <sway_top>
 *   <sway_top: 1.5 10>  (Sways 1.5x default speed, up to 10 degrees)
 *   <sway_top: 0.8 3.5> (Sways 0.8x default speed, up to 3.5 degrees)
 *
 * --- Plugin Parameters ---
 * You can set the default sway behavior in the plugin manager:
 *
 * - Default Sway Speed:
 *   The base speed for the sway animation. Higher values mean faster swaying.
 *
 * - Default Sway Angle (Degrees):
 *   The default maximum angle of sway, in degrees.
 *
 * --- Compatibility ---
 * This plugin modifies the behavior of Sprite_Character. It should be
 * generally compatible with other plugins, but if another plugin heavily
 * manipulates sprite rotation for events, there might be conflicts.
 * Place this plugin lower in the list if you suspect conflicts.
 *
 * The sway effect is applied by rotating the entire event sprite.
 * The sprite's anchor is at its bottom-center (0.5, 1), so rotation
 * naturally makes the top part move more, simulating a sway.
 *
 * ---
 *
 * @param defaultSwaySpeed
 * @text Default Sway Speed
 * @desc The default speed factor for swaying. E.g., 1.0.
 * @type number
 * @decimals 2
 * @default 0.25
 *
 * @param defaultSwayAngle
 * @text Default Sway Angle (Degrees)
 * @desc The default maximum sway angle in degrees. E.g., 5.0.
 * @type number
 * @decimals 1
 * @default 1.25
 */

(function () {
    'use strict';

    const scriptName = Utils.extractFileName(document.currentScript.src);
    const parameters = PluginManager.parameters(scriptName);
    const defaultSwaySpeed = Number(parameters.defaultSwaySpeed) || 0.25;
    const defaultSwayAngle = Number(parameters.defaultSwayAngle) || 1.25;

    // --- Sprite_Character Modifications ---

    Sprite_Character.prototype.initSwayParameters = function () {
        this._swayEnabled = false;

        if (this._character && this._character instanceof Game_Event) {
            const eventData = this._character.event();
            if (eventData && eventData.note) {
                const note = eventData.note;
                const paramsSwayMatch = note.match(
                    /<sway_top\s*:\s*(\\d+(?:\\.\\d+)?)\s+(\\d+(?:\\.\\d+)?)\s*>/i
                );

                if (paramsSwayMatch) {
                    this._swayEnabled = true;
                    this._swaySpeed = parseFloat(paramsSwayMatch[1]);
                    this._swayMaxAngleRad = (parseFloat(paramsSwayMatch[2]) * Math.PI) / 180.0;
                } else {
                    const simpleSwayMatch = note.match(/<sway_top>/i);
                    if (simpleSwayMatch) {
                        this._swayEnabled = true;
                        this._swaySpeed = defaultSwaySpeed;
                        this._swayMaxAngleRad = (defaultSwayAngle * Math.PI) / 180.0;
                    }
                }

                if (this._swayEnabled) {
                    this._swayInitialPhaseOffset = Math.random() * Math.PI * 2; // Randomize start of sway
                }
            }
        }

        // If sway is not enabled (or no longer enabled), reset rotation.
        if (!this._swayEnabled) {
            this.rotation = 0;
        }
    };

    const _Sprite_Character_initialize = Sprite_Character.prototype.initialize;
    Sprite_Character.prototype.initialize = function (character) {
        _Sprite_Character_initialize.call(this, character);
        this.initSwayParameters();
    };

    const _Sprite_Character_setCharacter = Sprite_Character.prototype.setCharacter;
    Sprite_Character.prototype.setCharacter = function (character) {
        _Sprite_Character_setCharacter.call(this, character);
        // Re-initialize sway parameters when the character (event page) changes
        this.initSwayParameters();
    };

    const _Sprite_Character_update = Sprite_Character.prototype.update;
    Sprite_Character.prototype.update = function () {
        _Sprite_Character_update.call(this);

        if (this._swayEnabled && this.visible) {
            // A speed of 1.0 means one full sway cycle takes approx 150 frames (2.5 seconds at 60fps)
            const timeFactor = Math.PI / 75;
            const currentPhase = Graphics.frameCount * timeFactor * this._swaySpeed;
            const swayValue = Math.sin(currentPhase + this._swayInitialPhaseOffset);
            this.rotation = swayValue * this._swayMaxAngleRad;
        }
        // If _swayEnabled is false, initSwayParameters would have set rotation to 0.
    };
})();
