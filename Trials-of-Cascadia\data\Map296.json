{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "Cobblestones5", "battleback2Name": "Town2", "bgm": {"name": "RobotWorld", "pan": 0, "pitch": 100, "volume": 20}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 50, "note": "<Zoom: 200%>\n<Hide Tile Shadows>\n<Fog 1 Settings>\n Name: !wispy_white_clouds\n Opacity: 100\n Vert Scroll: 1.0\nColor Tone: 55, -155, 55, 255\n</Fog 1 Settings>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 50, "width": 50, "data": [6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6728, 3744, 3728, 3752, 6720, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6708, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6742, 3744, 3728, 3752, 6744, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6732, 6712, 6704, 6704, 6728, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7094, 3744, 3728, 3752, 7091, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7090, 7098, 7098, 7098, 7098, 7098, 7098, 7090, 7090, 7090, 6720, 6704, 6704, 6728, 7088, 7088, 7088, 7088, 7096, 7096, 7088, 7088, 7088, 7088, 7088, 7096, 7096, 7088, 7088, 7088, 7092, 3744, 3728, 3752, 7089, 7088, 7088, 4595, 4594, 4594, 4594, 4594, 4598, 7088, 4595, 4594, 4594, 4594, 4594, 4598, 7088, 4595, 4594, 4594, 4594, 4594, 4598, 7088, 7088, 7088, 6720, 6704, 6704, 6728, 7088, 7088, 7096, 7096, 5075, 5074, 5074, 5074, 5074, 5074, 5074, 5074, 5078, 7096, 7096, 7088, 7092, 3744, 3728, 3752, 7089, 7088, 7088, 4593, 4592, 4592, 4592, 4592, 4596, 7088, 4593, 4592, 4592, 4592, 4592, 4596, 7088, 4593, 4592, 4592, 4592, 4592, 4596, 7088, 7088, 7088, 6720, 6704, 6704, 6728, 3680, 3680, 3761, 3773, 5073, 5072, 5072, 5072, 5072, 5072, 5072, 5072, 5076, 3771, 3773, 3722, 3704, 3744, 3728, 3752, 3696, 3680, 3680, 4601, 4600, 4600, 4600, 4600, 4604, 3680, 4601, 4600, 4600, 4600, 4600, 4604, 3680, 4601, 4600, 4600, 4600, 4600, 4604, 3680, 3680, 3680, 6720, 6704, 6704, 6728, 3680, 3680, 7043, 7040, 7040, 5080, 5080, 5080, 5080, 5080, 5080, 5080, 7042, 7040, 7046, 3712, 3704, 3744, 3728, 3734, 3689, 3680, 3680, 7043, 7042, 7042, 7042, 7042, 7046, 3680, 7043, 7042, 7042, 7042, 7042, 7046, 3680, 7043, 7042, 7042, 7042, 7042, 7046, 3680, 3680, 3680, 6720, 6704, 6704, 6728, 3680, 3680, 7041, 7040, 7040, 7046, 7042, 7042, 7042, 7042, 7042, 7043, 7040, 7040, 7044, 3712, 3704, 3744, 3728, 3752, 3696, 3680, 3680, 7041, 7040, 7040, 7040, 7040, 7044, 3680, 7041, 7040, 7040, 7040, 7040, 7044, 3680, 7041, 7040, 7040, 7040, 7040, 7044, 3680, 3680, 3680, 6720, 6704, 6704, 6728, 3680, 3680, 7041, 7040, 7040, 7044, 7040, 7040, 7040, 7040, 7040, 7041, 7040, 7040, 7044, 3712, 3704, 3744, 3728, 3752, 3696, 3680, 3682, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 6720, 6704, 6704, 6728, 3708, 3708, 7041, 7040, 7040, 7044, 3708, 3708, 3708, 3708, 3708, 7041, 7040, 7040, 7044, 3724, 3718, 3744, 3728, 3752, 3720, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3688, 3680, 3680, 6720, 6704, 6704, 6728, 3748, 3748, 3748, 3748, 3748, 3748, 3750, 3748, 3748, 3748, 3749, 3748, 3748, 3748, 3748, 3761, 3761, 3737, 3728, 3734, 3761, 3750, 3748, 3748, 3749, 3761, 3761, 3761, 3750, 3748, 3748, 3748, 3748, 3748, 3751, 3748, 3748, 3748, 3748, 3748, 3748, 3748, 3748, 3680, 3680, 3680, 6720, 6704, 6704, 6728, 5074, 5074, 5074, 5074, 5074, 5078, 3744, 3728, 3728, 3728, 3752, 4595, 4594, 4594, 4594, 4594, 4598, 3744, 3728, 3752, 3714, 3728, 3728, 3728, 3728, 3700, 3700, 3700, 5075, 5074, 5074, 5074, 5074, 5078, 3760, 5075, 5074, 5074, 5074, 5078, 5075, 5078, 3744, 3728, 3680, 3680, 6720, 6704, 6704, 6728, 5072, 5072, 5072, 5072, 5072, 5076, 3768, 3736, 3732, 3756, 3766, 4593, 4592, 4592, 4592, 4592, 4596, 3744, 3728, 3752, 3696, 3756, 3756, 3756, 3756, 3680, 3680, 3680, 5073, 5072, 5072, 5072, 5072, 5076, 3760, 5073, 5072, 5072, 5072, 5072, 5072, 5076, 3744, 3728, 3680, 3680, 6720, 6704, 6704, 6728, 5080, 5080, 5080, 5080, 5080, 5084, 3722, 3744, 3752, 3714, 3716, 4601, 4600, 4600, 4600, 4600, 4604, 3746, 3756, 3766, 3696, 7043, 7042, 7042, 7046, 3680, 3684, 3708, 5081, 5080, 5080, 5080, 5080, 5084, 3760, 5081, 5080, 5080, 5080, 5080, 5080, 5084, 3768, 3756, 3680, 3680, 6720, 6704, 6704, 6728, 6690, 6676, 6676, 6676, 6676, 6692, 3712, 3744, 3752, 3696, 3704, 6690, 6676, 6676, 6676, 6676, 6692, 3760, 5075, 5074, 5078, 7041, 7040, 7040, 7044, 5074, 5074, 5078, 6690, 6676, 6676, 6676, 6676, 6692, 3760, 6690, 6676, 6676, 6676, 6676, 6676, 6676, 6676, 6692, 3680, 3680, 6720, 6704, 6704, 6728, 4595, 4594, 4594, 4594, 4594, 4598, 3712, 3744, 3752, 3720, 3705, 6696, 6684, 6684, 6684, 6684, 6694, 3760, 5073, 5072, 5076, 7041, 7040, 7040, 7044, 5073, 5072, 5076, 6696, 6684, 6684, 6684, 6684, 6694, 3760, 6696, 6684, 6684, 6684, 6684, 6664, 6656, 6656, 6680, 3680, 3680, 6720, 6704, 6704, 6728, 4593, 4592, 4592, 4592, 4592, 4596, 3712, 3768, 3738, 3764, 3721, 3708, 3708, 3708, 3708, 3708, 3718, 3760, 5073, 5072, 5076, 7041, 7040, 7040, 7044, 5073, 5072, 5076, 3666, 3652, 3652, 3652, 3652, 3668, 3760, 3720, 3688, 3680, 3680, 3680, 6672, 6656, 6656, 6680, 3680, 3680, 6720, 6704, 6704, 6728, 4601, 4600, 4600, 4600, 4600, 4604, 3697, 3716, 3744, 3734, 3761, 3761, 3761, 3761, 3761, 3761, 3761, 3755, 5081, 5080, 5084, 7041, 7040, 7040, 7044, 5081, 5080, 5084, 3648, 3632, 3632, 3632, 3632, 3656, 3747, 3773, 3720, 3708, 3720, 3720, 6696, 6684, 6684, 6694, 3680, 3680, 6720, 6704, 6704, 6728, 6690, 6676, 6676, 6676, 6676, 6692, 3696, 3704, 3744, 3752, 3715, 3700, 3700, 3700, 3700, 3700, 3716, 3760, 7043, 7042, 7046, 4008, 3996, 3996, 4006, 7043, 7042, 7046, 3648, 3632, 3632, 3632, 3632, 3656, 3760, 4595, 4594, 4594, 4594, 4598, 3770, 5075, 5074, 5074, 5074, 5074, 6720, 6704, 6704, 6728, 6696, 6684, 6684, 6684, 6684, 6694, 3698, 3718, 3744, 3752, 3712, 5075, 5074, 5074, 5074, 5074, 5078, 3760, 7041, 7040, 7044, 7043, 7042, 7042, 7046, 7041, 7040, 7052, 5075, 5074, 5074, 5074, 5074, 5078, 3760, 4593, 4592, 4592, 4592, 4596, 3760, 5073, 5072, 5072, 5072, 5072, 6720, 6704, 6704, 6728, 3680, 3680, 3680, 3680, 3680, 3684, 3719, 3762, 3729, 3752, 3712, 5073, 5072, 5072, 5072, 5072, 5076, 3760, 3720, 3708, 3718, 7041, 7040, 7040, 7044, 3720, 3710, 3717, 5073, 5072, 5072, 5072, 5072, 5076, 3760, 4601, 4600, 4600, 4600, 4604, 3760, 5081, 5080, 5080, 5080, 5080, 6720, 6704, 6704, 6728, 3680, 3728, 3728, 3728, 3728, 3704, 3762, 3729, 3732, 3766, 3712, 5081, 5080, 5080, 5080, 5080, 5084, 3769, 3761, 3750, 3748, 3748, 3748, 3748, 3748, 3748, 3764, 3697, 5081, 5080, 5080, 5080, 5080, 5084, 3760, 7043, 7042, 7042, 7042, 7046, 3760, 7043, 7042, 7042, 7042, 7042, 6720, 6704, 6704, 6728, 3680, 3728, 3728, 3728, 3728, 3704, 3744, 3728, 3752, 3714, 3706, 6690, 6676, 6676, 6676, 6676, 6692, 3700, 3716, 3744, 3728, 3728, 3728, 3728, 3728, 3728, 3752, 3696, 7043, 7042, 7042, 7042, 7042, 7046, 3760, 7041, 7040, 7040, 7040, 7044, 3760, 7041, 7040, 7040, 7040, 7044, 6720, 6704, 6704, 6728, 3680, 3756, 3756, 3756, 3756, 3704, 3746, 3728, 3752, 3696, 3704, 6696, 6684, 6684, 6684, 6684, 6694, 3680, 3704, 3744, 3732, 3756, 3756, 3756, 3756, 3736, 3752, 3696, 7041, 7040, 7040, 7040, 7040, 7044, 3745, 3728, 3728, 3728, 3728, 3728, 3728, 3728, 3728, 3728, 3728, 3732, 6720, 6704, 6704, 6728, 3680, 7043, 7042, 7042, 7046, 3704, 3760, 3696, 3680, 3681, 3682, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3704, 3744, 3752, 3715, 3713, 3713, 3717, 3744, 3752, 3696, 3680, 3680, 3748, 3748, 3748, 3748, 3733, 3756, 3736, 3728, 3728, 3732, 3756, 3756, 3736, 3728, 3728, 3752, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 3704, 3760, 6702, 853, 854, 6702, 3680, 3680, 6702, 853, 854, 6702, 3680, 3704, 3744, 3752, 3712, 1537, 1537, 3712, 3744, 3752, 3696, 3680, 3680, 3728, 3728, 3728, 3728, 3752, 3714, 3728, 3728, 3728, 3728, 3700, 3700, 3728, 3728, 3728, 3752, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 3704, 3760, 860, 852, 855, 863, 3680, 3680, 860, 852, 855, 863, 3680, 3704, 3744, 3752, 3712, 1537, 1537, 3712, 3744, 3752, 3696, 3680, 3680, 3728, 3728, 3728, 3728, 3752, 3696, 3756, 3756, 3756, 3756, 3680, 3680, 3756, 3756, 3756, 3766, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 3704, 3760, 6690, 6676, 6676, 6692, 3680, 3680, 6690, 6676, 6676, 6692, 3680, 3704, 3744, 3752, 3721, 3713, 3713, 3719, 3744, 3752, 3696, 3680, 3680, 3756, 3756, 3756, 3756, 3753, 3696, 6690, 6676, 6676, 6692, 3680, 3680, 6690, 6676, 6676, 6692, 6720, 6704, 6704, 6728, 3680, 3680, 3680, 3680, 3680, 3704, 3760, 6696, 6684, 6684, 6694, 3680, 3680, 6696, 6684, 6684, 6694, 3680, 3704, 3744, 3730, 3748, 3748, 3748, 3748, 3729, 3752, 3696, 3680, 3680, 6690, 6676, 6676, 6692, 3760, 3696, 6672, 6656, 6656, 6680, 3680, 3680, 6672, 6656, 6656, 6680, 6720, 6704, 6704, 6728, 3680, 3728, 3728, 3728, 3728, 3704, 3760, 3720, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3718, 3744, 3728, 3728, 3728, 3728, 3728, 3728, 3752, 3696, 3680, 3680, 6672, 6656, 6656, 6680, 3760, 3696, 6672, 6656, 6656, 6680, 3680, 3680, 6672, 6656, 6656, 6680, 6720, 6704, 6704, 6728, 3680, 3728, 3728, 3728, 3728, 3704, 3747, 3761, 3761, 3761, 3761, 3761, 3761, 3761, 3761, 3750, 3749, 3761, 3761, 3757, 3756, 3756, 3756, 3756, 3736, 3728, 3752, 3720, 3708, 3708, 6672, 6656, 6656, 6680, 3760, 3720, 6696, 6684, 6684, 6694, 3708, 3708, 6696, 6684, 6684, 6694, 6720, 6704, 6704, 6728, 3680, 3756, 3756, 3756, 3756, 3704, 3760, 3714, 5075, 5074, 5074, 5074, 5074, 5078, 5079, 3744, 3728, 3700, 3700, 3700, 3700, 3700, 3700, 3700, 3728, 3732, 3758, 3761, 3761, 3765, 6696, 6684, 6684, 6694, 3747, 3761, 3761, 3761, 3761, 3761, 3761, 3761, 3761, 3761, 3761, 3773, 6720, 6704, 6704, 6728, 3680, 7043, 7042, 7042, 7046, 3704, 3760, 3696, 5073, 5072, 5072, 5072, 5072, 5072, 5076, 3768, 3756, 3680, 4595, 4594, 4594, 4594, 4598, 3680, 5075, 5074, 5074, 5074, 5078, 3760, 3696, 3680, 3680, 3704, 3760, 4595, 4594, 4594, 4594, 4598, 3700, 4595, 4594, 4594, 4594, 4598, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 3704, 3760, 3696, 5081, 5080, 5080, 5080, 5080, 5084, 7040, 7040, 7047, 3680, 4593, 4592, 4592, 4592, 4596, 3680, 5073, 5072, 5072, 5072, 5076, 3760, 3720, 3708, 3708, 3718, 3760, 4593, 4592, 4592, 4592, 4596, 3680, 4593, 4592, 4592, 4592, 4596, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 3704, 3760, 3696, 7043, 7042, 7042, 7042, 7042, 7041, 7040, 7040, 7044, 3680, 4601, 4600, 4600, 4600, 4604, 3680, 5081, 5080, 5080, 5080, 5084, 3745, 3748, 3748, 3748, 3748, 3754, 4601, 4600, 4600, 4600, 4604, 3680, 4601, 4600, 4600, 4600, 4604, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 3704, 3760, 3696, 7041, 7040, 7040, 7040, 7040, 7041, 7040, 7040, 7044, 3680, 7043, 7042, 7042, 7042, 7046, 3680, 7043, 7042, 7042, 7042, 7046, 3768, 3736, 3728, 3728, 3728, 3752, 6690, 6676, 6676, 6676, 6692, 3680, 6690, 6676, 6676, 6676, 6692, 6720, 6704, 6704, 6728, 3680, 3680, 3680, 3680, 3680, 3704, 3760, 3720, 3708, 3708, 3708, 3688, 3680, 7041, 7040, 7040, 7044, 3680, 7041, 7040, 7040, 7040, 7044, 3680, 7041, 7040, 7040, 7040, 7044, 3716, 3744, 3728, 3728, 3728, 3752, 6696, 6684, 6684, 6684, 6694, 3680, 4595, 4594, 4594, 4594, 4598, 6720, 6704, 6704, 6728, 3680, 3728, 3728, 3728, 3728, 3704, 3745, 3748, 3748, 3748, 3764, 3720, 3710, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3708, 3688, 3680, 3680, 3680, 3704, 3744, 3728, 3728, 3728, 3752, 3696, 3680, 3680, 3680, 3680, 3680, 4593, 4592, 4592, 4592, 4596, 6720, 6704, 6704, 6728, 3680, 3728, 3728, 3728, 3728, 3704, 3768, 3756, 3756, 3756, 3758, 3761, 3761, 3759, 3761, 3761, 3761, 3750, 3748, 3748, 3748, 3748, 3748, 3748, 3764, 3720, 3708, 3708, 3708, 3718, 3744, 3728, 3728, 3728, 3752, 3720, 3708, 3708, 3708, 3708, 3708, 4601, 4600, 4600, 4600, 4604, 6720, 6704, 6704, 6728, 3680, 3728, 3732, 3756, 3680, 3682, 3700, 3700, 3700, 3700, 3700, 4595, 4594, 4594, 4594, 4594, 4598, 3768, 3736, 3728, 3728, 3728, 3728, 3728, 3730, 3748, 3748, 3748, 3748, 3748, 3729, 3728, 3728, 3728, 3730, 3748, 3748, 3748, 3748, 3748, 3764, 6690, 6676, 6676, 6676, 6692, 6720, 6704, 6704, 6728, 3680, 3728, 3752, 5075, 5074, 5074, 5074, 5074, 5074, 5078, 3680, 4593, 4592, 4592, 4592, 4592, 4596, 3716, 3768, 3756, 3756, 3756, 3756, 3756, 3756, 3756, 3756, 3756, 3756, 3756, 3736, 3728, 3728, 3732, 3756, 3756, 3756, 3756, 3756, 3756, 3766, 4595, 4594, 4594, 4594, 4598, 6720, 6704, 6704, 6728, 3680, 3756, 3766, 5073, 5072, 5072, 5072, 5072, 5072, 5076, 3680, 4601, 4600, 4600, 4600, 4600, 4604, 3682, 3700, 3700, 3700, 3700, 3700, 3700, 3700, 3700, 3700, 3700, 3700, 3700, 3728, 3732, 3756, 3756, 3700, 3700, 3700, 3700, 3700, 3700, 3700, 4593, 4592, 4592, 4592, 4596, 6720, 6704, 6704, 6728, 3680, 7043, 7040, 7040, 5081, 5080, 5080, 5080, 5080, 5084, 3680, 7043, 7042, 7042, 7042, 7042, 7046, 3680, 3728, 3728, 3728, 3728, 3680, 3680, 3728, 3728, 3728, 3728, 3680, 3680, 3728, 3752, 5075, 5074, 5074, 5074, 5074, 5074, 5078, 5079, 3680, 4601, 4600, 4600, 4600, 4604, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 7042, 7042, 7042, 7042, 7046, 3680, 7041, 7040, 7040, 7040, 7040, 7044, 3680, 3728, 3728, 3728, 3728, 3680, 3680, 3728, 3728, 3728, 3728, 3680, 3680, 3728, 3752, 5073, 5072, 5072, 5072, 5072, 5072, 5072, 5076, 3680, 6690, 6676, 6676, 6676, 6692, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 7040, 7040, 7040, 7040, 7044, 3680, 3761, 3761, 3761, 3761, 3761, 3761, 3741, 3756, 3756, 3756, 3756, 3680, 3680, 3756, 3756, 3756, 3756, 3680, 3680, 3756, 3766, 5081, 5080, 5080, 5080, 5080, 5080, 5080, 5084, 3680, 6696, 6684, 6684, 6684, 6694, 6720, 6704, 6704, 6728, 3680, 7041, 7040, 7040, 7044, 3680, 3680, 3680, 3680, 3680, 3680, 4595, 4594, 4594, 4594, 4594, 4598, 3760, 7043, 7042, 7042, 7046, 3680, 3680, 7043, 7042, 7042, 7046, 3680, 3680, 7043, 7042, 7042, 7046, 7042, 7042, 7042, 7042, 7042, 7046, 3680, 3680, 3680, 3680, 3680, 3680, 6720, 6704, 6704, 6728, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 4593, 4592, 4592, 4592, 4592, 4596, 3760, 7041, 7040, 7040, 7044, 3680, 3680, 7041, 7040, 7040, 7044, 3680, 3680, 7041, 7040, 7040, 7044, 7040, 7040, 7040, 7040, 7040, 7044, 3680, 3680, 3680, 3680, 3680, 3680, 6720, 6704, 6704, 6728, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 3680, 4601, 4600, 4600, 4600, 4600, 4604, 3772, 7041, 7040, 7040, 7044, 3680, 3680, 7041, 7040, 7040, 7044, 3680, 3680, 7041, 7040, 7040, 7044, 3771, 3761, 3761, 3761, 3761, 3761, 3680, 3680, 3680, 3680, 3680, 3680, 6720, 6704, 6704, 6706, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6724, 6705, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 6704, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 584, 585, 586, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3947, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 592, 593, 594, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 600, 601, 602, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 545, 0, 3939, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 608, 609, 610, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 0, 0, 0, 56, 0, 0, 0, 0, 553, 0, 3964, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 616, 617, 618, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3954, 3940, 3946, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 619, 620, 621, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 3963, 3965, 0, 0, 0, 0, 0, 3954, 3941, 3965, 0, 0, 0, 0, 0, 3963, 3953, 3949, 3948, 3934, 3965, 0, 3966, 0, 0, 3963, 3965, 0, 3966, 0, 627, 628, 629, 3963, 3943, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3960, 3945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3939, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 603, 604, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3935, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 611, 612, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3954, 3956, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3955, 3949, 3958, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3954, 3940, 3940, 3956, 0, 0, 3963, 3957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 6, 0, 6, 579, 0, 0, 0, 3938, 3948, 3928, 3926, 3953, 3957, 0, 3952, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 0, 14, 587, 0, 0, 0, 3952, 0, 3960, 3958, 0, 3961, 3943, 3959, 0, 0, 594, 10, 0, 10, 0, 3963, 3957, 0, 0, 0, 3963, 3957, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3961, 3957, 0, 0, 0, 0, 3952, 0, 0, 0, 602, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 3961, 3965, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 3961, 3957, 0, 0, 610, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 523, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 18, 19, 18, 19, 0, 0, 3937, 3956, 0, 0, 0, 0, 3954, 3946, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 531, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 0, 0, 0, 0, 26, 27, 26, 27, 0, 0, 3936, 3944, 0, 3962, 0, 0, 3960, 3945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 539, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3938, 3950, 3953, 3947, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3939, 3953, 3965, 0, 3963, 3965, 0, 0, 0, 0, 0, 0, 3963, 3959, 0, 0, 3961, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 513, 561, 562, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3965, 0, 0, 0, 10, 0, 0, 3963, 3965, 0, 0, 3963, 3953, 3965, 520, 521, 569, 570, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 18, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3953, 3942, 3956, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 587, 0, 0, 0, 0, 0, 0, 0, 26, 27, 0, 0, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0, 3960, 3945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 10, 0, 0, 0, 10, 0, 10, 0, 0, 3962, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3961, 3943, 3965, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 3963, 3951, 3965, 0, 3963, 3953, 3965, 0, 3963, 3943, 3953, 3965, 0, 3963, 3953, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 619, 620, 621, 0, 0, 0, 0, 0, 3963, 3957, 0, 0, 0, 0, 0, 0, 0, 3963, 3953, 3943, 3953, 3957, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 627, 628, 629, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 619, 620, 621, 3964, 0, 3964, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 627, 628, 629, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 561, 562, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 569, 570, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 577, 578, 0, 0, 0, 0, 0, 0, 579, 512, 513, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 561, 562, 587, 520, 521, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 569, 570, 587, 528, 529, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 945, 0, 931, 0, 0, 0, 0, 0, 0, 0, 930, 0, 948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 545, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 945, 939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 552, 553, 0, 841, 842, 0, 0, 0, 0, 0, 0, 0, 841, 842, 0, 512, 513, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 561, 952, 848, 849, 850, 851, 0, 0, 0, 0, 0, 848, 849, 850, 851, 520, 521, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 930, 0, 0, 0, 0, 0, 0, 0, 0, 0, 960, 0, 56, 56, 0, 0, 0, 0, 0, 0, 0, 56, 56, 0, 528, 529, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 537, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 944, 0, 0, 0, 948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 520, 521, 0, 0, 0, 0, 0, 0, 0, 0, 0, 621, 0, 946, 947, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 528, 529, 0, 0, 0, 0, 0, 0, 0, 0, 0, 629, 0, 516, 955, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 537, 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 532, 532, 0, 0, 0, 0, 0, 0, 0, 952, 0, 853, 854, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 624, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 540, 540, 0, 0, 0, 0, 0, 0, 0, 521, 860, 861, 862, 863, 357, 358, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 632, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 529, 0, 0, 0, 0, 365, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 569, 570, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 537, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 579, 0, 532, 532, 0, 577, 578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 248, 0, 0, 248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 587, 0, 540, 540, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 587, 0, 0, 0, 0, 0, 0, 0, 0, 585, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 593, 0, 0, 0, 0, 0, 0, 520, 521, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 601, 0, 0, 0, 0, 0, 0, 528, 529, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 609, 0, 0, 0, 0, 0, 0, 536, 537, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 619, 620, 587, 0, 0, 0, 0, 0, 931, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 945, 0, 0, 0, 949, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 601, 0, 0, 0, 0, 948, 0, 0, 627, 628, 587, 0, 0, 0, 0, 0, 939, 578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 957, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 609, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 948, 0, 0, 0, 0, 0, 948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 617, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 948, 0, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 624, 0, 0, 0, 0, 0, 0, 965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 513, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 520, 521, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 954, 0, 0, 0, 0, 0, 0, 18, 19, 528, 529, 18, 19, 0, 0, 0, 0, 0, 0, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 961, 587, 0, 0, 0, 0, 0, 0, 26, 27, 536, 537, 26, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 624, 0, 0, 0, 0, 0, 0, 0, 632, 0, 0, 960, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 632, 0, 0, 940, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 561, 562, 561, 562, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 569, 570, 569, 570, 0, 0, 0, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 577, 578, 577, 578, 0, 0, 0, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 579, 0, 0, 512, 513, 0, 0, 0, 0, 0, 0, 0, 0, 624, 0, 0, 0, 0, 0, 948, 0, 0, 945, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 513, 0, 520, 521, 544, 545, 0, 0, 0, 0, 0, 0, 632, 0, 0, 0, 0, 0, 948, 545, 0, 0, 0, 0, 0, 512, 513, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 12, 0, 0, 0, 0, 0, 561, 562, 520, 521, 0, 528, 529, 552, 553, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 553, 523, 0, 0, 0, 0, 520, 521, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 619, 620, 621, 619, 620, 621, 0, 0, 0, 0, 569, 570, 528, 529, 0, 536, 537, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 56, 0, 0, 531, 0, 56, 56, 0, 528, 529, 0, 56, 56, 0, 0, 0, 0, 0, 0, 0, 627, 628, 629, 627, 628, 629, 0, 0, 0, 0, 577, 578, 536, 537, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 624, 0, 0, 0, 539, 0, 0, 0, 0, 536, 537, 0, 0, 624, 0, 0, 0, 0, 0, 0, 0, 635, 636, 637, 635, 636, 637, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 0, 0, 0, 0, 930, 931, 0, 0, 0, 0, 0, 0, 0, 930, 931, 0, 0, 148, 0, 0, 0, 147, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 0, 140, 139, 140, 0, 0, 937, 938, 939, 940, 0, 619, 620, 621, 0, 937, 938, 939, 940, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 0, 944, 571, 938, 939, 948, 949, 627, 628, 629, 944, 945, 938, 939, 571, 949, 0, 0, 0, 0, 0, 0, 523, 0, 555, 0, 0, 0, 0, 0, 0, 0, 0, 0, 547, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 0, 952, 945, 938, 931, 0, 0, 0, 555, 0, 0, 0, 930, 939, 948, 957, 0, 0, 0, 0, 561, 562, 531, 0, 563, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 291, 292, 0, 0, 140, 139, 140, 523, 952, 945, 938, 939, 940, 0, 0, 563, 0, 0, 937, 938, 547, 948, 957, 0, 0, 0, 0, 569, 570, 539, 0, 0, 0, 0, 0, 0, 294, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 299, 300, 0, 0, 140, 139, 140, 531, 952, 945, 946, 947, 948, 949, 0, 0, 0, 944, 945, 946, 947, 948, 957, 0, 0, 0, 0, 577, 578, 0, 0, 2, 0, 0, 2, 0, 302, 0, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0, 140, 139, 140, 539, 562, 953, 954, 955, 956, 957, 56, 0, 56, 952, 953, 954, 955, 956, 957, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 544, 545, 0, 0, 10, 0, 0, 544, 545, 0, 0, 10, 0, 0, 10, 931, 0, 0, 0, 0, 140, 139, 140, 569, 570, 961, 532, 532, 964, 965, 0, 0, 0, 960, 961, 532, 532, 964, 965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 552, 553, 0, 0, 0, 0, 0, 552, 553, 0, 0, 0, 0, 937, 938, 939, 940, 0, 0, 0, 140, 139, 140, 577, 578, 624, 540, 540, 0, 0, 0, 0, 0, 0, 0, 540, 540, 0, 0, 0, 0, 0, 0, 0, 0, 930, 931, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 619, 620, 621, 945, 938, 939, 571, 949, 0, 0, 140, 139, 140, 0, 0, 632, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 937, 938, 939, 940, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 627, 628, 629, 945, 938, 939, 948, 957, 0, 0, 140, 139, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 513, 0, 0, 0, 0, 571, 0, 0, 0, 0, 944, 945, 938, 939, 948, 949, 0, 0, 555, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 555, 0, 930, 939, 948, 957, 0, 0, 140, 139, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 513, 0, 0, 0, 0, 0, 0, 0, 619, 620, 952, 945, 0, 0, 948, 957, 307, 308, 563, 0, 0, 0, 547, 0, 0, 0, 571, 0, 0, 563, 937, 938, 939, 948, 957, 0, 0, 140, 139, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 520, 521, 0, 0, 0, 0, 0, 0, 0, 627, 628, 952, 953, 954, 517, 956, 957, 315, 316, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 944, 945, 938, 939, 948, 957, 0, 0, 140, 139, 140, 0, 624, 0, 0, 579, 0, 0, 0, 0, 528, 529, 0, 532, 532, 0, 0, 0, 0, 0, 0, 960, 961, 524, 525, 964, 965, 0, 0, 0, 579, 512, 513, 523, 0, 0, 0, 0, 523, 512, 513, 945, 946, 947, 948, 957, 0, 0, 140, 139, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 537, 0, 540, 540, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 545, 587, 520, 521, 531, 624, 0, 0, 0, 531, 520, 952, 953, 954, 955, 956, 957, 0, 0, 140, 139, 140, 0, 0, 0, 0, 0, 0, 561, 562, 0, 0, 0, 619, 620, 621, 619, 620, 621, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 553, 587, 528, 529, 539, 632, 0, 0, 0, 539, 528, 960, 961, 18, 19, 964, 965, 0, 0, 140, 139, 140, 0, 0, 0, 0, 0, 0, 561, 562, 0, 0, 0, 627, 628, 629, 627, 628, 629, 0, 0, 0, 0, 200, 0, 0, 201, 0, 0, 0, 0, 587, 512, 513, 523, 293, 0, 0, 0, 0, 536, 537, 0, 26, 27, 0, 0, 0, 0, 140, 139, 140, 0, 0, 0, 0, 0, 0, 569, 570, 0, 0, 0, 635, 636, 637, 635, 636, 637, 0, 0, 532, 0, 224, 248, 248, 225, 0, 532, 0, 545, 587, 520, 521, 531, 301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 0, 0, 930, 931, 0, 624, 577, 578, 0, 0, 0, 0, 571, 0, 0, 0, 0, 0, 0, 540, 0, 625, 0, 0, 625, 0, 540, 0, 0, 0, 0, 0, 571, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 555, 0, 0, 0, 140, 139, 140, 0, 937, 938, 939, 940, 632, 0, 0, 0, 0, 0, 0, 0, 0, 0, 547, 0, 512, 513, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 563, 0, 0, 0, 140, 139, 140, 944, 945, 938, 939, 948, 949, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 513, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 19, 579, 0, 0, 0, 4, 0, 0, 0, 0, 140, 139, 140, 952, 945, 938, 939, 948, 957, 0, 0, 930, 931, 0, 0, 0, 0, 930, 931, 0, 520, 521, 0, 0, 0, 0, 0, 0, 0, 0, 0, 585, 0, 0, 930, 931, 0, 0, 0, 26, 27, 587, 0, 0, 357, 358, 0, 561, 562, 0, 140, 139, 140, 952, 945, 938, 939, 948, 957, 0, 937, 938, 939, 940, 0, 0, 937, 938, 939, 940, 528, 529, 0, 0, 0, 0, 0, 0, 0, 0, 0, 593, 0, 937, 938, 939, 940, 0, 0, 0, 930, 931, 0, 0, 365, 366, 930, 569, 570, 0, 140, 139, 140, 952, 945, 946, 947, 948, 957, 944, 945, 938, 939, 948, 949, 944, 555, 938, 939, 948, 536, 537, 0, 0, 0, 0, 0, 0, 0, 0, 0, 523, 944, 945, 938, 939, 547, 949, 0, 937, 938, 939, 940, 0, 0, 937, 938, 577, 940, 0, 140, 139, 140, 952, 953, 954, 955, 956, 957, 952, 945, 946, 947, 948, 293, 952, 563, 946, 947, 948, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 531, 952, 945, 938, 939, 948, 957, 944, 945, 938, 939, 555, 949, 944, 945, 938, 939, 571, 0, 140, 139, 140, 960, 961, 930, 931, 964, 965, 952, 953, 954, 955, 956, 301, 952, 953, 954, 955, 956, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 539, 952, 571, 938, 939, 948, 957, 952, 945, 938, 939, 563, 957, 952, 547, 938, 939, 948, 0, 140, 139, 140, 0, 937, 938, 939, 940, 0, 960, 961, 0, 579, 964, 293, 960, 961, 0, 0, 964, 965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 952, 945, 938, 939, 948, 957, 952, 945, 946, 947, 948, 957, 952, 945, 946, 947, 948, 0, 140, 139, 140, 944, 945, 938, 939, 948, 949, 0, 0, 0, 587, 624, 301, 0, 0, 930, 931, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 952, 945, 946, 947, 948, 957, 952, 953, 954, 955, 956, 957, 952, 953, 954, 955, 956, 0, 140, 139, 140, 952, 945, 938, 939, 948, 957, 0, 0, 0, 587, 632, 0, 0, 937, 938, 939, 940, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 952, 953, 579, 955, 956, 957, 960, 961, 0, 0, 964, 965, 960, 961, 0, 0, 964, 0, 140, 139, 140, 952, 547, 938, 939, 948, 957, 0, 0, 0, 587, 0, 0, 944, 945, 938, 939, 571, 949, 0, 0, 512, 513, 0, 0, 619, 620, 621, 0, 0, 960, 512, 513, 0, 964, 965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 952, 945, 938, 939, 948, 957, 0, 585, 0, 0, 0, 0, 0, 0, 930, 939, 948, 957, 0, 0, 520, 521, 0, 0, 627, 628, 629, 0, 0, 0, 520, 521, 0, 0, 0, 0, 0, 363, 364, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 952, 945, 946, 947, 948, 957, 0, 593, 0, 0, 547, 0, 0, 937, 938, 939, 948, 957, 0, 547, 0, 0, 0, 0, 0, 0, 0, 624, 0, 0, 528, 529, 0, 0, 0, 0, 555, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 952, 953, 954, 955, 956, 957, 0, 601, 0, 0, 0, 0, 944, 945, 946, 947, 948, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 632, 0, 0, 536, 537, 0, 0, 0, 0, 563, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 960, 961, 579, 0, 964, 965, 0, 609, 0, 624, 0, 0, 952, 953, 954, 955, 956, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 0, 0, 930, 931, 293, 0, 0, 617, 357, 358, 0, 561, 562, 961, 532, 532, 964, 965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 513, 0, 0, 0, 0, 0, 0, 0, 512, 513, 0, 0, 0, 0, 0, 0, 579, 0, 0, 140, 139, 140, 0, 937, 938, 939, 301, 0, 0, 0, 365, 366, 0, 569, 570, 0, 540, 540, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 520, 521, 0, 0, 0, 0, 0, 0, 0, 520, 521, 0, 545, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 944, 945, 938, 939, 948, 949, 0, 0, 0, 0, 0, 577, 578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 528, 529, 0, 0, 930, 931, 0, 0, 0, 528, 529, 552, 553, 0, 0, 0, 0, 571, 0, 0, 140, 139, 140, 952, 547, 938, 939, 948, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 537, 0, 937, 938, 939, 940, 0, 0, 536, 537, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 952, 945, 938, 939, 948, 957, 619, 620, 621, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 930, 931, 0, 0, 0, 0, 930, 931, 0, 0, 944, 945, 938, 939, 948, 949, 0, 0, 0, 0, 0, 0, 0, 579, 0, 545, 0, 0, 140, 139, 140, 952, 945, 938, 931, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 555, 0, 0, 937, 938, 939, 940, 0, 0, 937, 938, 939, 940, 0, 952, 555, 938, 939, 948, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 952, 945, 938, 939, 940, 0, 0, 571, 0, 0, 0, 0, 0, 0, 0, 563, 0, 944, 945, 938, 939, 948, 949, 944, 945, 938, 939, 948, 949, 952, 563, 938, 939, 948, 957, 0, 0, 0, 363, 364, 0, 0, 555, 0, 0, 0, 0, 140, 139, 140, 952, 945, 946, 947, 948, 949, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 952, 945, 938, 939, 948, 957, 952, 555, 938, 939, 948, 957, 952, 945, 938, 931, 0, 0, 0, 0, 0, 0, 0, 0, 0, 563, 0, 0, 0, 0, 140, 139, 140, 952, 953, 961, 955, 956, 957, 0, 0, 0, 0, 0, 0, 0, 357, 358, 0, 0, 952, 945, 938, 939, 555, 957, 952, 563, 938, 939, 948, 957, 952, 945, 938, 939, 940, 0, 0, 0, 0, 571, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 960, 961, 587, 0, 964, 965, 0, 0, 0, 0, 0, 0, 0, 365, 366, 0, 0, 952, 945, 938, 939, 563, 957, 952, 945, 938, 939, 948, 957, 952, 945, 938, 939, 948, 949, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 0, 0, 587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 952, 945, 946, 947, 948, 957, 952, 945, 946, 947, 948, 957, 952, 945, 946, 947, 948, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 0, 0, 587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 571, 0, 0, 0, 0, 952, 953, 954, 955, 956, 957, 952, 953, 954, 955, 956, 957, 952, 953, 954, 955, 956, 957, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 139, 140, 129, 129, 587, 129, 129, 129, 129, 129, 129, 129, 129, 129, 129, 129, 129, 129, 129, 960, 961, 129, 129, 964, 965, 960, 961, 129, 129, 964, 965, 960, 961, 129, 129, 964, 965, 129, 129, 129, 129, 129, 129, 129, 129, 129, 129, 129, 0, 140, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 147, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 137, 148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "WARF_0015_ls-blue[VS8]", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Guard"]}, {"code": 401, "indent": 0, "parameters": ["State your business in Cogsburgh. All visitors must"]}, {"code": 401, "indent": 0, "parameters": ["register their intentions and declare any magical items."]}, {"code": 401, "indent": 0, "parameters": ["Contraband will be confiscated."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["We come for trade and information. We carry no"]}, {"code": 401, "indent": 0, "parameters": ["contraband and respect the laws of Cogsburgh."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Guard"]}, {"code": 401, "indent": 0, "parameters": ["Very well. You may enter."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 0}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Mayor's Wife1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["//The audience cheers"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Dr. <PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Fine people of Cogsburgh, I stand before you today as a"]}, {"code": 401, "indent": 0, "parameters": ["fellow citizen of our great city, and as a member of the"]}, {"code": 401, "indent": 0, "parameters": ["Council of Representatives."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>. <PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Our alliance with the Nation of Light is one of mutual"]}, {"code": 401, "indent": 0, "parameters": ["benefit and respect."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>. <PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Under the treaty, we have maintained our independence, our"]}, {"code": 401, "indent": 0, "parameters": ["culture, and our technological advancements. In return, we"]}, {"code": 401, "indent": 0, "parameters": ["provide our expertise and resources to support the"]}, {"code": 401, "indent": 0, "parameters": ["Emperor's mission of unity and peace."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>. <PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["However, there are those who seek to disrupt this harmony."]}, {"code": 401, "indent": 0, "parameters": ["These rebels, who call themselves the resistance, endanger"]}, {"code": 401, "indent": 0, "parameters": ["our safety and prosperity. They spread fear and chaos,"]}, {"code": 401, "indent": 0, "parameters": ["undermining the stability we have worked so hard to achieve."]}, {"code": 108, "indent": 0, "parameters": ["//The audience boos"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>. <PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["But rest assured, my fellow citizens, we will not be"]}, {"code": 401, "indent": 0, "parameters": ["swayed by their treachery. We will stand firm and uphold"]}, {"code": 401, "indent": 0, "parameters": ["the principles of our alliance. Together with the Nation"]}, {"code": 401, "indent": 0, "parameters": ["of Light, we will ensure that peace and order prevail."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>. <PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Thank you for your continued support and cooperation."]}, {"code": 401, "indent": 0, "parameters": ["Together, we will build a brighter future for Cogsburgh"]}, {"code": 401, "indent": 0, "parameters": ["and for all under the light of our great nation."]}, {"code": 108, "indent": 0, "parameters": ["//The audience cheers and <PERSON><PERSON> departs from the balcony back inside"]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What a two-faced liar. He makes me want to"]}, {"code": 401, "indent": 0, "parameters": ["throw up."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON> acts like a nice guy, but behind the"]}, {"code": 401, "indent": 0, "parameters": ["mask, he's a ruthless politician who would"]}, {"code": 401, "indent": 0, "parameters": ["stab his own mother in the back, if it meant"]}, {"code": 401, "indent": 0, "parameters": ["lining his pockets a little extra."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["This whole part of the continent used to be"]}, {"code": 401, "indent": 0, "parameters": ["elven settlements, but the Emperor told Grent"]}, {"code": 401, "indent": 0, "parameters": ["that the land was his to do with as he wished."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["My family was among the first to be displaced. I saw"]}, {"code": 401, "indent": 0, "parameters": ["firsthand how <PERSON><PERSON><PERSON>s promises turned into threats and"]}, {"code": 401, "indent": 0, "parameters": ["violence."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["He sends his greasy little henchmen to get"]}, {"code": 401, "indent": 0, "parameters": ["their hands dirty, all while washing his own"]}, {"code": 401, "indent": 0, "parameters": ["of any accountability."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Are you making all of these dirt and oil puns because of"]}, {"code": 401, "indent": 0, "parameters": ["steampunk stuff?"]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What?? No! We don't have time for puns right"]}, {"code": 401, "indent": 0, "parameters": ["now. We need to figure out a way to access"]}, {"code": 401, "indent": 0, "parameters": ["the high security prison where they're keeping"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON> and the villages."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Let's find this <PERSON><PERSON> and see what she knows"]}, {"code": 401, "indent": 0, "parameters": ["about the city. We must act swiftly. Our people"]}, {"code": 401, "indent": 0, "parameters": ["are counting on us, and <PERSON>'s airship is"]}, {"code": 401, "indent": 0, "parameters": ["paramount to our mission."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 0}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Green Paladin1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Hawk"]}, {"code": 401, "indent": 0, "parameters": ["Hold it right there! You lot look far too suspicious for"]}, {"code": 401, "indent": 0, "parameters": ["my liking."]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["We look suspicious?! Who the heck are you?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Hawk"]}, {"code": 401, "indent": 0, "parameters": ["Name's <PERSON>. I'm Dr<PERSON> <PERSON><PERSON>'s personal assistant. He noticed"]}, {"code": 401, "indent": 0, "parameters": ["you during his speech and didn't like the look of you."]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["We're just passing through. We mean no harm."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Hawk"]}, {"code": 401, "indent": 0, "parameters": ["Passing through, huh? You seem awfully interested in"]}, {"code": 401, "indent": 0, "parameters": ["Cogsburgh for mere travelers. What's your business here?"]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["None of your business, that's what!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Hawk"]}, {"code": 401, "indent": 0, "parameters": ["Feisty, aren't we? Well, <PERSON><PERSON> doesn't like strangers"]}, {"code": 401, "indent": 0, "parameters": ["poking around where they don't belong."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Hawk"]}, {"code": 401, "indent": 0, "parameters": ["Now, you can either come with us quietly, or we can do this"]}, {"code": 401, "indent": 0, "parameters": ["the hard way."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Hawk"]}, {"code": 401, "indent": 0, "parameters": ["You may have won this round, but you won't get far. <PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["will hear about this."]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["That was close. We need to be more careful."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON>'s forces are on high alert now. We must"]}, {"code": 401, "indent": 0, "parameters": ["move quickly and quietly."]}, {"code": 101, "indent": 0, "parameters": ["zayne-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Let's find that clock tower before they come"]}, {"code": 401, "indent": 0, "parameters": ["back with more goons!"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 0}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-HL Bar Dancing Girl-walking", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>! You made it! And with backup, I see."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON>! We're here to help rescue <PERSON> and"]}, {"code": 401, "indent": 0, "parameters": ["the other resistance members. "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Dad's a brilliant engineer, but <PERSON><PERSON>'s been forcing him to"]}, {"code": 401, "indent": 0, "parameters": ["work on weapons of war, even going far enough to intimidate"]}, {"code": 401, "indent": 0, "parameters": ["my mom and I! We have to show him that he can't just"]}, {"code": 401, "indent": 0, "parameters": ["strongarm the <PERSON><PERSON><PERSON>'s around like that!"]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["We also need to free the villagers. What do you"]}, {"code": 401, "indent": 0, "parameters": ["know about the prison's layout and defenses?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["The prison is heavily guarded, but there's a way to bypass"]}, {"code": 401, "indent": 0, "parameters": ["most of the defenses. We'll need to use the old clock tower"]}, {"code": 401, "indent": 0, "parameters": ["to parachute onto the roof."]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Parachute? That's a new one. How do we do that?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["They're an invention my father came up with that let you"]}, {"code": 401, "indent": 0, "parameters": ["float! Climb to the top and jump from there. It's risky,"]}, {"code": 401, "indent": 0, "parameters": ["but it's the best way to avoid detection."]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["And...\\! how are we supposed to get out?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Don't worry! Dad will know how to get out. He always has"]}, {"code": 401, "indent": 0, "parameters": ["some kind of backup plan!"]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["We can handle it. Thank you, <PERSON><PERSON>."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Please be safe!"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 0}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "be<PERSON><PERSON>-walking[VS8]", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Who in the heck are you? Have we met"]}, {"code": 401, "indent": 0, "parameters": ["somewhere before?"]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Are you Bernard? We're here to free you from"]}, {"code": 401, "indent": 0, "parameters": ["this place!"]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Well technically, I am <PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON> IV. My great grandfather was the"]}, {"code": 401, "indent": 0, "parameters": ["original <PERSON>, and my"]}, {"code": 401, "indent": 0, "parameters": ["grandfather was <PERSON>."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What?! What does that have to do with anything?"]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Oh sorry! Sometimes I ramble a bit."]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON>, we're here to get you out! Your"]}, {"code": 401, "indent": 0, "parameters": ["daughter, <PERSON><PERSON>, sent us!"]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON>? My <PERSON><PERSON> sent you?\\! Oh, thank the"]}, {"code": 401, "indent": 0, "parameters": ["stars! I feared I'd never see her again.."]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["You know, she was always the clever one. Even"]}, {"code": 401, "indent": 0, "parameters": ["as a child, she would take apart toys just to"]}, {"code": 401, "indent": 0, "parameters": ["see how they worked. One time, she dismantled"]}, {"code": 401, "indent": 0, "parameters": ["the entire clocktower mechanism and..."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["*Ahem*"]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON>, we need to focus. We're here to stop"]}, {"code": 401, "indent": 0, "parameters": ["Grent and free the people of Cogsburgh."]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Grent... he betrayed me. Forced me to make"]}, {"code": 401, "indent": 0, "parameters": ["weapons of war, or he would use <PERSON><PERSON> as"]}, {"code": 401, "indent": 0, "parameters": ["leverage. He threatened my daughter to ensure"]}, {"code": 401, "indent": 0, "parameters": ["my compliance."]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["That's despicable! We won't let him get away"]}, {"code": 401, "indent": 0, "parameters": ["with this!"]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Thank you. <PERSON><PERSON> has been incredibly brave,"]}, {"code": 401, "indent": 0, "parameters": ["working undercover to help the resistance."]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["She provided you with the parachutes,"]}, {"code": 401, "indent": 0, "parameters": ["didn’t she? Clever girl."]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["I invented those you know! I figured if there"]}, {"code": 401, "indent": 0, "parameters": ["was resistance against the natural forces of"]}, {"code": 401, "indent": 0, "parameters": ["the air pressure, you could slow the rate of"]}, {"code": 401, "indent": 0, "parameters": ["descent by approximately 3500 kilodecimals..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Anyways! <PERSON><PERSON> has been using my inventions to strengthen"]}, {"code": 401, "indent": 0, "parameters": ["his hold over Cogsburgh. He's diverted my efforts from"]}, {"code": 401, "indent": 0, "parameters": ["legitimate scientific research into weapons of oppression."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["I've tried to sabotage his work whenever possible, but"]}, {"code": 401, "indent": 0, "parameters": ["it's difficult when he's constantly watching over me."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["We need to stop him! Can you help us disable"]}, {"code": 401, "indent": 0, "parameters": ["his operation once and for all?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Yes! But first, we need to get out of here. I have a plan,"]}, {"code": 401, "indent": 0, "parameters": ["but it involves getting to the central control room."]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Why? Do we need to disable the power so we can"]}, {"code": 401, "indent": 0, "parameters": ["get you out of that cell?"]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Hmm? Cell? What are you talking about, young"]}, {"code": 401, "indent": 0, "parameters": ["man?\\! Oh, this thing! That's not an issue."]}, {"code": 108, "indent": 0, "parameters": ["\\\\<PERSON><PERSON><PERSON> takes out a small device and presses a few buttons, and then the forcefield to the cell dissipates"]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Wait, you could just do that? Why didn't you"]}, {"code": 401, "indent": 0, "parameters": ["escape earlier?"]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Well you see, I was waiting for reinforcements."]}, {"code": 401, "indent": 0, "parameters": ["There's only one way we're getting out of here,"]}, {"code": 401, "indent": 0, "parameters": ["and that's through the front door."]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Through the front door??\\! That sounds like my"]}, {"code": 401, "indent": 0, "parameters": ["kind of strategy! None of this stealth stuff!"]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Yes, unfortunately I haven't invented any"]}, {"code": 401, "indent": 0, "parameters": ["parachutes that can go back up yet, so we'll"]}, {"code": 401, "indent": 0, "parameters": ["have to go out instead."]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Oh, and one more thing. The alarm will sound"]}, {"code": 401, "indent": 0, "parameters": ["in about...\\! three seconds."]}, {"code": 108, "indent": 0, "parameters": ["alarm sounds"]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Great..."]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["If we can get to the control room, I can"]}, {"code": 401, "indent": 0, "parameters": ["upload a virus that will shut this whole"]}, {"code": 401, "indent": 0, "parameters": ["entire factory down for good! Let's go!"]}, {"code": 108, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON> joins the party"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["We made it! This is the central control room!"]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Now, let's get to work. I just need a moment"]}, {"code": 401, "indent": 0, "parameters": ["to upload the virus."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Wait, do you hear that? Someone's coming!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["Well, well. If it isn't <PERSON> and his merry band of"]}, {"code": 401, "indent": 0, "parameters": ["rescuers. I must admit, your tenacity is impressive."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["But I'm afraid this is as far as you go."]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["We're not backing down, <PERSON><PERSON>! Your reign of"]}, {"code": 401, "indent": 0, "parameters": ["terror ends right here!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["Terror? My dear boy, I bring order and progress to this"]}, {"code": 401, "indent": 0, "parameters": ["city. It's you and your little \"resistance\" that threaten"]}, {"code": 401, "indent": 0, "parameters": ["our stability."]}, {"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Order?! You've twisted my inventions into"]}, {"code": 401, "indent": 0, "parameters": ["tools of oppression to suit your own ends!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["Tools of oppression? No, <PERSON>. Tools of power. With your"]}, {"code": 401, "indent": 0, "parameters": ["inventions, we can maintain control and ensure the safety"]}, {"code": 401, "indent": 0, "parameters": ["and prosperity of Cogsburgh."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["You see, progress requires sacrifice. The weak must be"]}, {"code": 401, "indent": 0, "parameters": ["guided, controlled, for their own good. The resistance"]}, {"code": 401, "indent": 0, "parameters": ["stands in the way of that progress."]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["You're delusional if you think your actions"]}, {"code": 401, "indent": 0, "parameters": ["are for the good of the people!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["Delusional? No, my dear. I am a visionary. I see a future"]}, {"code": 401, "indent": 0, "parameters": ["where Cogsburgh is the pinnacle of technological"]}, {"code": 401, "indent": 0, "parameters": ["advancement, a beacon of light in a dark world."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["The Emperor understands this. He sees the potential in our"]}, {"code": 401, "indent": 0, "parameters": ["work, the potential for true power."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["With our technology, we can ensure that no one opposes us."]}, {"code": 401, "indent": 0, "parameters": ["We can bring about an era of unprecedented peace and order."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Peace through control, is just another way to"]}, {"code": 401, "indent": 0, "parameters": ["say war."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["You are naive, girl. Idealistic. The world is not a place"]}, {"code": 401, "indent": 0, "parameters": ["for idealists. It is a place for those who seize power"]}, {"code": 401, "indent": 0, "parameters": ["and use it to shape the future."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["Enough talk. Let me show you what the latest model of"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON> can do, if you won't listen to reason!"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["be<PERSON><PERSON>-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Alright, the virus is uploading... and... done!"]}, {"code": 401, "indent": 0, "parameters": ["This will cripple their operations for good."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Grent"]}, {"code": 401, "indent": 0, "parameters": ["This isn't over! You can't stop progress!"]}, {"code": 101, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Great job, <PERSON>! Now, let's get out of here"]}, {"code": 401, "indent": 0, "parameters": ["before more of <PERSON><PERSON>'s goons show up."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 0}, {"id": 6, "name": "EV006", "note": "<Breathing Rate: 1%>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Item Girl1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Hey there! Don't forget to stock up!"]}, {"code": 302, "indent": 0, "parameters": [0, 7, 0, 0, false]}, {"code": 605, "indent": 0, "parameters": [0, 8, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 10, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 12, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 11, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 19, 0, 0]}, {"code": 605, "indent": 0, "parameters": [0, 20, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 10, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Blacksmith Girl1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_4_RandomizeShop", "ThisEventCreateNewRandomizedShop", "This Event: Create New Randomized Shop", {"Shop": "", "ItemIDs:arraynum": "[]", "ItemRandTotal:eval": "8", "WeaponIDs:arraynum": "[]", "WeaponRandTotal:eval": "8", "ArmorIDs:arraynum": "[\"7\",\"8\",\"9\",\"10\",\"26\",\"27\",\"28\",\"29\",\"46\",\"47\",\"48\",\"49\",\"66\",\"67\",\"68\",\"69\",\"86\",\"87\",\"88\",\"89\",\"106\",\"107\",\"108\",\"109\",\"126\",\"127\",\"128\",\"129\",\"146\",\"147\",\"148\",\"149\"]", "ArmorRandTotal:eval": "8", "Settings": "", "OpenAfter:eval": "true", "AbsoluteMax:eval": "16", "AllowDuplicates:eval": "false", "PurchaseOnly:eval": "false", "AllowVariance:eval": "true", "PriceVariance:eval": "0.20", "PriceRate:eval": "1.00"}]}, {"code": 657, "indent": 0, "parameters": ["Shop = "]}, {"code": 657, "indent": 0, "parameters": ["Item ID(s) = []"]}, {"code": 657, "indent": 0, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 0, "parameters": ["Weapon ID(s) = []"]}, {"code": 657, "indent": 0, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 0, "parameters": ["Armor ID(s) = [\"7\",\"8\",\"9\",\"10\",\"26\",\"27\",\"28\",\"29\",\"46\",\"4…"]}, {"code": 657, "indent": 0, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 0, "parameters": ["Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Open After? = true"]}, {"code": 657, "indent": 0, "parameters": ["Absolute Maximum = 16"]}, {"code": 657, "indent": 0, "parameters": ["Allow Duplicates? = false"]}, {"code": 657, "indent": 0, "parameters": ["Purchase Only? = false"]}, {"code": 657, "indent": 0, "parameters": ["Price Variance? = true"]}, {"code": 657, "indent": 0, "parameters": ["Variance Rate = 0.20"]}, {"code": 657, "indent": 0, "parameters": ["Price Rate = 1.00"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Blacksmith1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["What would you like to do?"]}, {"code": 102, "indent": 0, "parameters": [["Shop", "<PERSON>roll"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Shop"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_4_RandomizeShop", "ThisEventReopenLastShop", "This Event: Open Last Shop", {}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<PERSON>roll"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_4_RandomizeShop", "ThisEventCreateNewRandomizedShop", "This Event: Create New Randomized Shop", {"Shop": "", "ItemIDs:arraynum": "[]", "ItemRandTotal:eval": "8", "WeaponIDs:arraynum": "[\"7\",\"8\",\"9\",\"10\",\"26\",\"27\",\"28\",\"29\",\"46\",\"47\",\"48\",\"49\",\"66\",\"67\",\"68\",\"69\",\"86\",\"87\",\"88\",\"89\"]", "WeaponRandTotal:eval": "8", "ArmorIDs:arraynum": "[]", "ArmorRandTotal:eval": "8", "Settings": "", "OpenAfter:eval": "true", "AbsoluteMax:eval": "16", "AllowDuplicates:eval": "false", "PurchaseOnly:eval": "false", "AllowVariance:eval": "true", "PriceVariance:eval": "0.20", "PriceRate:eval": "1.00"}]}, {"code": 657, "indent": 1, "parameters": ["Shop = "]}, {"code": 657, "indent": 1, "parameters": ["Item ID(s) = []"]}, {"code": 657, "indent": 1, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 1, "parameters": ["Weapon ID(s) = [\"7\",\"8\",\"9\",\"10\",\"26\",\"27\",\"28\",\"29\",\"46\",\"…"]}, {"code": 657, "indent": 1, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 1, "parameters": ["Armor ID(s) = []"]}, {"code": 657, "indent": 1, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 1, "parameters": ["Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Open After? = true"]}, {"code": 657, "indent": 1, "parameters": ["Absolute Maximum = 16"]}, {"code": 657, "indent": 1, "parameters": ["Allow Duplicates? = false"]}, {"code": 657, "indent": 1, "parameters": ["Purchase Only? = false"]}, {"code": 657, "indent": 1, "parameters": ["Price Variance? = true"]}, {"code": 657, "indent": 1, "parameters": ["Variance Rate = 0.20"]}, {"code": 657, "indent": 1, "parameters": ["Price Rate = 1.00"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Blacksmith1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 0}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-HLAccessory", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_4_RandomizeShop", "ThisEventCreateNewRandomizedShop", "This Event: Create New Randomized Shop", {"Shop": "", "ItemIDs:arraynum": "[]", "ItemRandTotal:eval": "8", "WeaponIDs:arraynum": "[]", "WeaponRandTotal:eval": "8", "ArmorIDs:arraynum": "[\"306\",\"307\",\"308\",\"309\",\"310\",\"311\",\"312\",\"313\",\"314\",\"315\",\"316\",\"317\",\"318\",\"319\",\"320\",\"321\",\"322\",\"323\",\"324\",\"325\",\"326\",\"327\",\"328\",\"329\",\"330\",\"331\",\"332\",\"333\",\"334\",\"335\",\"336\",\"337\",\"338\",\"339\",\"341\",\"342\",\"344\",\"345\"]", "ArmorRandTotal:eval": "8", "Settings": "", "OpenAfter:eval": "true", "AbsoluteMax:eval": "16", "AllowDuplicates:eval": "false", "PurchaseOnly:eval": "false", "AllowVariance:eval": "true", "PriceVariance:eval": "0.20", "PriceRate:eval": "1.00"}]}, {"code": 657, "indent": 0, "parameters": ["Shop = "]}, {"code": 657, "indent": 0, "parameters": ["Item ID(s) = []"]}, {"code": 657, "indent": 0, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 0, "parameters": ["Weapon ID(s) = []"]}, {"code": 657, "indent": 0, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 0, "parameters": ["Armor ID(s) = [\"306\",\"307\",\"308\",\"309\",\"310\",\"311\",\"312\",\"3…"]}, {"code": 657, "indent": 0, "parameters": ["Total # = 8"]}, {"code": 657, "indent": 0, "parameters": ["Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Open After? = true"]}, {"code": 657, "indent": 0, "parameters": ["Absolute Maximum = 16"]}, {"code": 657, "indent": 0, "parameters": ["Allow Duplicates? = false"]}, {"code": 657, "indent": 0, "parameters": ["Purchase Only? = false"]}, {"code": 657, "indent": 0, "parameters": ["Price Variance? = true"]}, {"code": 657, "indent": 0, "parameters": ["Variance Rate = 0.20"]}, {"code": 657, "indent": 0, "parameters": ["Price Rate = 1.00"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 0}, {"id": 10, "name": "EV010", "note": "<Sprite Offset X: +24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door5", "direction": 6, "pattern": 0, "characterIndex": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 323, 25, 39, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 24, "y": 20}, {"id": 11, "name": "EV011", "note": "<Sprite Offset X: +24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door5", "direction": 6, "pattern": 0, "characterIndex": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Scale_ChangeFor_Event", "Scale: Change for Event", {"EventID:eval": "0", "ScaleX:eval": "2.00", "ScaleY:eval": "1.00", "Duration:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Scale X = 2.00"]}, {"code": 657, "indent": 0, "parameters": ["Scale Y = 1.00"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 24, "y": 17}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door2", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 8}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 47}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 47}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 47}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 47}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 47}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 30, "y": 47}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 47}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 47}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 35, "y": 47}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 36, "y": 47}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 47}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 47}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 129, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 47}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 7}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Roof_Windows", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 16}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Roof_Windows", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 16}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Night", "Duration:num": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Night"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 357, "indent": 0, "parameters": ["Wave 5/VisuMZ_2_HorrorEffects", "MapColorCreate", "Map: Color Effect Create", {"type:str": "Polaroid"}]}, {"code": 657, "indent": 0, "parameters": ["Effect Type = Polaroid"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 1}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 50>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 65%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 85%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 19}, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 50>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 65%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 85%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 19}, {"id": 32, "name": "EV032", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 300>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 40%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 40%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 22}, {"id": 33, "name": "EV033", "note": "<Sprite Offset X: +24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door3", "direction": 2, "pattern": 0, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 7}, {"id": 34, "name": "EV034", "note": "<Sprite Offset X: +24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door3", "direction": 2, "pattern": 0, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 34, "y": 7}, {"id": 35, "name": "EV035", "note": "<Sprite Offset X: +24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door3", "direction": 2, "pattern": 0, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 41, "y": 7}, {"id": 36, "name": "EV036", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Signs", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 6}, {"id": 37, "name": "EV037", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Signs", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 6}, {"id": 38, "name": "EV038", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 33, "y": 7}, {"id": 39, "name": "EV039", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 36, "y": 7}, {"id": 40, "name": "EV040", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 7}, {"id": 41, "name": "EV041", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 7}, {"id": 42, "name": "EV042", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 28}, {"id": 43, "name": "EV043", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 23}, {"id": 44, "name": "EV044", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 34, "y": 31}, {"id": 45, "name": "EV045", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 23}, {"id": 46, "name": "EV046", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 44, "y": 22}, {"id": 47, "name": "EV047", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 46, "y": 22}, {"id": 48, "name": "EV048", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 36}, {"id": 49, "name": "EV049", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 17}, {"id": 50, "name": "EV050", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 17}, {"id": 51, "name": "EV051", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 44, "y": 44}, {"id": 52, "name": "EV052", "note": "<Sprite Offset: +24, +0>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 38, "y": 22}, {"id": 53, "name": "EV053", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 46, "y": 44}, {"id": 54, "name": "EV054", "note": "<Sprite Offset: +24, +0>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 17}, {"id": 55, "name": "EV055", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 36}, {"id": 56, "name": "EV056", "note": "<Sprite Offset: +24, +0>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 30}, {"id": 57, "name": "EV057", "note": "<Sprite Offset: +24, +0>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 44, "y": 30}, {"id": 58, "name": "EV058", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 36}, {"id": 59, "name": "EV059", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 36}, {"id": 60, "name": "EV060", "note": "<Sprite Offset: +24, +0>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 28}, {"id": 61, "name": "EV061", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 28}, {"id": 62, "name": "EV062", "note": "<Sprite Offset: +24, +0>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 26}, {"id": 63, "name": "EV063", "note": "<Sprite Offset: +24, +0>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 26}, {"id": 64, "name": "EV064", "note": "<Sprite Offset: +24, +0>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 35}, {"id": 65, "name": "Replica 1 - <PERSON> Mother", "note": "<shadow><spark>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The baby's crying. I can hear it from here."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["I left the rice cooking. It's probably burning now."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["Why is the sky so dark? It's not evening yet."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["I keep walking but the house never gets closer."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 108, "indent": 0, "parameters": ["<Compass Icon: 1922>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON>ert <PERSON>o<PERSON>>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Range: 5>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 360>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 301, "indent": 0, "parameters": [0, 83, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 45, "parameters": ["Move to: $gameVariables.value(101), $gameVariables.value(102)"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 24, "y": 28}, {"id": 66, "name": "Replica 2 - The Shopkeeper", "note": "<shadow><spark>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The morning customers will be waiting."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["I forgot to lock the safe last night."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The street signs are all wrong today."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["My feet feel heavy. Why are my feet so heavy?"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 108, "indent": 0, "parameters": ["<Compass Icon: 1922>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON>ert <PERSON>o<PERSON>>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Range: 5>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 360>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 301, "indent": 0, "parameters": [0, 83, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 45, "parameters": ["Move to: $gameVariables.value(101), $gameVariables.value(102)"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 10, "y": 11}, {"id": 67, "name": "Replica 3 - <PERSON> Soldier", "note": "<shadow><spark>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The flag should be raised by now."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["My rifle feels... different. Lighter somehow."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The parade ground looks smaller than I remember."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["I can't find my unit. They must have moved without me."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 108, "indent": 0, "parameters": ["<Compass Icon: 1922>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON>ert <PERSON>o<PERSON>>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Range: 5>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 360>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 301, "indent": 0, "parameters": [0, 83, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 45, "parameters": ["Move to: $gameVariables.value(101), $gameVariables.value(102)"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 34, "y": 34}, {"id": 68, "name": "Replica 4 - <PERSON> Child", "note": "<shadow><spark>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The school bell rang ages ago."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["My lunchbox feels empty. Did I eat already?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The playground looks... broken somehow."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["I'm not supposed to be out here alone."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 108, "indent": 0, "parameters": ["<Compass Icon: 1922>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON>ert <PERSON>o<PERSON>>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Range: 5>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 360>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 301, "indent": 0, "parameters": [0, 83, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 45, "parameters": ["Move to: $gameVariables.value(101), $gameVariables.value(102)"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 42, "y": 23}, {"id": 69, "name": "Replica 5 - <PERSON>", "note": "<shadow><spark>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The morning gong should have sounded."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["My robes feel... wrong. Too stiff."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The temple bells are silent today."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["I can't remember the path to the meditation garden."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 108, "indent": 0, "parameters": ["<Compass Icon: 1922>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON>ert <PERSON>o<PERSON>>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Range: 5>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 360>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 301, "indent": 0, "parameters": [0, 83, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 45, "parameters": ["Move to: $gameVariables.value(101), $gameVariables.value(102)"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 9, "y": 21}, {"id": 70, "name": "Replica 6 - <PERSON> Lover", "note": "<shadow><spark>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["They're waiting at our usual spot."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The cherry tree looks... dead somehow."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["I can't find the love letter I wrote."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The river sounds different today. <PERSON><PERSON>."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 108, "indent": 0, "parameters": ["<Compass Icon: 1922>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON>ert <PERSON>o<PERSON>>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Range: 5>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 360>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 301, "indent": 0, "parameters": [0, 83, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 45, "parameters": ["Move to: $gameVariables.value(101), $gameVariables.value(102)"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 36, "y": 9}, {"id": 71, "name": "Replica 7 - <PERSON> Elder", "note": "<shadow><spark>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["My morning tea will be cold by now."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The neighborhood dogs aren't barking today."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["I can't remember which house is mine."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Replica"]}, {"code": 401, "indent": 0, "parameters": ["The air feels... thick. Hard to breathe."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 108, "indent": 0, "parameters": ["<Compass Icon: 1922>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON>ert <PERSON>o<PERSON>>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Range: 5>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 360>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 301, "indent": 0, "parameters": [0, 83, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 45, "parameters": ["Move to: $gameVariables.value(101), $gameVariables.value(102)"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 159, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 10, "y": 37}, {"id": 72, "name": "EV072", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 150>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 30%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 30%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 7}, {"id": 73, "name": "EV073", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [999]}, {"code": 121, "indent": 0, "parameters": [159, 159, 0]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 0, 102], 60, false]}, {"code": 357, "indent": 0, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"KILL! KILL! KILL!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"true\",\"SoundFilename:str\":\"\",\"SoundVolume:num\":\"90\",\"SoundPitch:num\":\"100\",\"SoundPan:num\":\"0\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Text = \"KILL! KILL! KILL!\""]}, {"code": 657, "indent": 0, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 0, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "65", "Hue:eval": "180"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 65"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 180"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "66", "Hue:eval": "180"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 66"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 180"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "67", "Hue:eval": "180"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 67"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 180"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "68", "Hue:eval": "180"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 68"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 180"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "69", "Hue:eval": "180"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 69"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 180"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "70", "Hue:eval": "180"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 70"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 180"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "71", "Hue:eval": "180"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 71"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 180"]}, {"code": 230, "indent": 0, "parameters": [999]}, {"code": 121, "indent": 0, "parameters": [159, 159, 1]}, {"code": 212, "indent": 0, "parameters": [65, 288, false]}, {"code": 212, "indent": 0, "parameters": [66, 288, false]}, {"code": 212, "indent": 0, "parameters": [67, 288, false]}, {"code": 212, "indent": 0, "parameters": [68, 288, false]}, {"code": 212, "indent": 0, "parameters": [69, 288, false]}, {"code": 212, "indent": 0, "parameters": [70, 288, false]}, {"code": 212, "indent": 0, "parameters": [71, 288, false]}, {"code": 205, "indent": 0, "parameters": [65, {"list": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [66, {"list": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [67, {"list": [{"code": 45, "parameters": ["Teleport to Home"], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["Teleport to Home"], "indent": 0}]}, {"code": 205, "indent": 0, "parameters": [68, {"list": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [69, {"list": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [70, {"list": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [71, {"list": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["Teleport to Home"], "indent": null}]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "65", "Hue:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 65"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "66", "Hue:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 66"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "67", "Hue:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 67"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "68", "Hue:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 68"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "69", "Hue:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 69"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "70", "Hue:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 70"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Hue_ChangeFor_Event", "Hue: Change for Event", {"EventID:eval": "71", "Hue:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 71"]}, {"code": 657, "indent": 0, "parameters": ["Hue = 0"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 2}, null, null, null, null, {"id": 78, "name": "EV078", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 200>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 40%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 40%>"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 16}, {"id": 79, "name": "EV079", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 200>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 40%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 40%>"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 38, "y": 16}, null, null, null, null, {"id": 84, "name": "EV084", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 200>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 40%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 40%>"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 30, "y": 28}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}