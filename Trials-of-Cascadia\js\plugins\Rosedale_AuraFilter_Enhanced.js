/*:============================================================================
*
* @target MZ
*
* <AUTHOR>
*
* @plugindesc | Enhanced Aura Filter : Version - 2.0.0 | Advanced aura filter with performance optimizations and new features.
*
* @url http://rosedale-studios.com
*
* @help
* ╔════════════════════════════════════╗
* ║ ()()                                                              ()() ║
* ║ (^.^)                    - Rosedale Studios -                    (^.^) ║
* ║c(")(")                                                          (")(")ↄ║
* ╚════════════════════════════════════╝

*============================================================================
*  Enhanced Features :
*============================================================================

*  ★ Performance Optimizations:
*    - Object pooling for filter instances
*    - Reduced shader complexity for better FPS
*    - Smart filter area calculations
*    - Batch processing for multiple auras
*
*  ★ New Features:
*    - Dynamic aura intensity based on character states
*    - Aura pulsing/breathing effects
*    - Distance-based aura scaling
*    - Conditional aura application
*    - Aura blending modes
*    - Performance monitoring
*
*  ★ Quality of Life:
*    - Better error handling
*    - Debug mode for development
*    - More intuitive plugin commands
*    - Enhanced documentation

*============================================================================
*  Requirements :
*============================================================================

*  This plugin requires RPG Maker MZ.

*============================================================================
*  Instructions :
*============================================================================

*   This enhanced plugin allows you to setup character/enemy auras via note tags,
* comments, states, and plugin commands! Auras are visual effects that can
* enhance your game's visuals with better performance than the original.

*  Note Tags (same as original):
*  <aura:NAME> - Apply to actors, enemies, or event pages

*  New Plugin Commands:
*  - set_aura_advanced - Advanced aura with parameters
*  - pulse_aura - Add pulsing effect to existing aura
*  - scale_aura - Scale aura based on distance
*  - conditional_aura - Apply aura only under certain conditions

*============================================================================
*  Terms Of Use :
*============================================================================

*   This Plugin may be used commercially, or non commercially. This plugin may
*  be extended upon. This plugin may NOT be shared, or passed to others
*  who have not purchased this product.

*============================================================================
*  Version History :
*============================================================================

*  ● Version : 2.0.0 (Enhanced)
*  ● Date : 2024
*    ★ Performance optimizations
*    ★ New advanced features
*    ★ Better code structure
*    ★ Enhanced documentation

*============================================================================

* @command set_aura_advanced
* @text Set Advanced Aura
* @desc Set aura with advanced parameters including intensity, pulsing, and conditions.

* @arg target
* @text Target Type
* @desc The type of target to apply aura to.
* @type select
* @option Event
* @value event
* @option Actor
* @value actor
* @option Enemy
* @value enemy
* @option Picture
* @value picture
* @default event

* @arg id
* @text Target ID
* @desc The ID of the target.
* @default 1
* @type number
* @min 1
* @max 10000

* @arg aura
* @text Aura Name
* @desc The name of the aura to use.
* @default
* @type text

* @arg intensity
* @text Intensity Multiplier
* @desc Intensity multiplier (0.1 to 2.0).
* @default 1.0
* @type number
* @decimals 1
* @min 0.1
* @max 2.0

* @arg pulse
* @text Pulsing Effect
* @desc Enable pulsing effect (true/false).
* @default false
* @type boolean

* @arg condition
* @text Condition Script
* @desc JavaScript condition for when to show aura.
* @default
* @type text

* @command pulse_aura
* @text Pulse Aura
* @desc Add pulsing effect to existing aura.

* @arg target
* @text Target Type
* @desc The type of target.
* @type select
* @option Event
* @value event
* @option Actor
* @value actor
* @option Enemy
* @value enemy
* @option Picture
* @value picture
* @default event

* @arg id
* @text Target ID
* @desc The ID of the target.
* @default 1
* @type number
* @min 1
* @max 10000

* @arg speed
* @text Pulse Speed
* @desc Speed of the pulse effect (0.1 to 5.0).
* @default 1.0
* @type number
* @decimals 1
* @min 0.1
* @max 5.0

* @command scale_aura
* @text Scale Aura by Distance
* @desc Scale aura intensity based on distance from player.

* @arg target
* @text Target Type
* @desc The type of target.
* @type select
* @option Event
* @value event
* @option Actor
* @value actor
* @option Enemy
* @value enemy
* @option Picture
* @value picture
* @default event

* @arg id
* @text Target ID
* @desc The ID of the target.
* @default 1
* @type number
* @min 1
* @max 10000

* @arg maxDistance
* @text Maximum Distance
* @desc Maximum distance for scaling effect.
* @default 10
* @type number
* @min 1
* @max 50

* @command clear_aura_advanced
* @text Clear Advanced Aura
* @desc Clear aura and all its effects.

* @arg target
* @text Target Type
* @desc The type of target.
* @type select
* @option Event
* @value event
* @option Actor
* @value actor
* @option Enemy
* @value enemy
* @option Picture
* @value picture
* @default event

* @arg id
* @text Target ID
* @desc The ID of the target.
* @default 1
* @type number
* @min 1
* @max 10000

* @param auras
* @text Auras
* @desc Define auras here (optimized for performance).
* @default ["{\"name\":\"red\",\"r\":\"255\",\"g\":\"0\",\"b\":\"0\",\"a\":\"0.50\",\"blendMode\":\"0\",\"performance\":\"high\"}","{\"name\":\"yellow\",\"r\":\"255\",\"g\":\"255\",\"b\":\"0\",\"a\":\"0.50\",\"blendMode\":\"0\",\"performance\":\"high\"}","{\"name\":\"green\",\"r\":\"0\",\"g\":\"255\",\"b\":\"0\",\"a\":\"0.50\",\"blendMode\":\"0\",\"performance\":\"high\"}","{\"name\":\"cyan\",\"r\":\"0\",\"g\":\"255\",\"b\":\"255\",\"a\":\"0.50\",\"blendMode\":\"0\",\"performance\":\"high\"}","{\"name\":\"blue\",\"r\":\"0\",\"g\":\"0\",\"b\":\"255\",\"a\":\"0.50\",\"blendMode\":\"0\",\"performance\":\"high\"}","{\"name\":\"purple\",\"r\":\"255\",\"g\":\"0\",\"b\":\"255\",\"a\":\"0.50\",\"blendMode\":\"0\",\"performance\":\"high\"}","{\"name\":\"white\",\"r\":\"255\",\"g\":\"255\",\"b\":\"255\",\"a\":\"0.50\",\"blendMode\":\"0\",\"performance\":\"high\"}"]
* @type struct<Aura>[]

* @param showOnMap
* @text Show Auras on Map
* @desc Should actor auras be shown on map.
* @default true
* @type boolean

* @param performanceMode
* @text Performance Mode
* @desc Performance optimization level.
* @type select
* @option High Quality
* @value high
* @option Balanced
* @value balanced
* @option Performance
* @value performance
* @default balanced

* @param maxAuras
* @text Maximum Active Auras
* @desc Maximum number of auras that can be active simultaneously.
* @default 50
* @type number
* @min 10
* @max 200

* @param enableDebug
* @text Enable Debug Mode
* @desc Enable debug information and performance monitoring.
* @default false
* @type boolean

*/

/*~struct~Aura:

* @param name
* @text Name
* @desc The name of the aura, used to set this aura via plugin command.
* @default Value
* @type text

* @param blendMode
* @text Blend Mode
* @desc Set the type of blend mode (0 is softer, 1 is more opaque).
* @default 0
* @type select
* @option Soft
* @value 0
* @option Opaque
* @value 1

* @param r
* @text Red
* @desc The red color of the aura.
* @default 255
* @type number
* @min 0
* @max 255

* @param g
* @text Green
* @desc The green color of the aura.
* @default 255
* @type number
* @min 0
* @max 255

* @param b
* @text Blue
* @desc The blue color of the aura.
* @default 255
* @type number
* @min 0
* @max 255

* @param a
* @text Intensity
* @desc How large will the aura be.
* @default 0.50
* @type number
* @decimals 2
* @min 0.00
* @max 1.00

* @param performance
* @text Performance Level
* @desc Performance level for this aura (high/balanced/performance).
* @default balanced
* @type select
* @option High Quality
* @value high
* @option Balanced
* @value balanced
* @option Performance
* @value performance

*/

//=============================================================================
// Enhanced Aura Filter System
//=============================================================================

// Performance monitoring
class AuraPerformanceMonitor {
    constructor() {
        this.frameCount = 0;
        this.lastTime = Date.now();
        this.fps = 60;
        this.activeAuras = 0;
        this.maxAuras = 0;
    }

    update() {
        this.frameCount++;
        const currentTime = Date.now();
        if (currentTime - this.lastTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
    }

    logPerformance() {
        if (Chaucer.auraFilter.params.enableDebug) {
            console.log(`Aura Performance - FPS: ${this.fps}, Active Auras: ${this.activeAuras}/${this.maxAuras}`);
        }
    }
}

// Object pool for filter instances
class AuraFilterPool {
    constructor() {
        this.pool = [];
        this.maxSize = 20;
    }

    get() {
        return this.pool.pop() || new Filter_Aura_Enhanced();
    }

    release(filter) {
        if (this.pool.length < this.maxSize) {
            filter.reset();
            this.pool.push(filter);
        }
    }

    clear() {
        this.pool.length = 0;
    }
}

// Enhanced Aura Filter
class Filter_Aura_Enhanced extends PIXI.Filter {
    static get vertexSrc() {
        if (Utils.RPGMAKER_NAME == 'MV') {
            return `
                attribute vec2 aVertexPosition;
                attribute vec2 aTextureCoord;
                uniform mat3 projectionMatrix;
                uniform mat3 filterMatrix;
                varying vec2 vTextureCoord;
                varying vec2 vFilterCoord;

                void main(void){
                    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
                    vFilterCoord = ( filterMatrix * vec3( aTextureCoord, 1.0)  ).xy;
                    vTextureCoord = aTextureCoord ;
                }
            `;
        } else {
            return `
                precision mediump float;
                attribute vec2 aVertexPosition;
                uniform mat3 projectionMatrix;
                varying vec2 vTextureCoord;
                varying vec2 fTextureCoord;
                uniform vec4 inputSize;
                uniform vec4 outputFrame;
                uniform vec4 filterArea;
                uniform vec2 dimensions;
                uniform vec3 zoom;

                vec4 filterVertexPosition( void ) {
                    vec2 position = aVertexPosition * outputFrame.zw + outputFrame.xy;
                    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
                }

                vec2 filterTextureCoord( void ) {
                    return aVertexPosition * (outputFrame.zw * inputSize.zw);
                }

                void main(void) {
                    gl_Position = filterVertexPosition();
                    vTextureCoord = filterTextureCoord();
                    vec2 z = ( zoom.xy * filterArea.xy ) / dimensions;
                    fTextureCoord = ( vTextureCoord.xy + z / 2.0 ) * ( 1.0 / zoom.z );
                }
            `;
        }
    }

    static fragmentSrc(blendMode, performanceMode) {
        const isPerformance = performanceMode === 'performance';
        const isBalanced = performanceMode === 'balanced';
        
        return `
            precision mediump float;
            varying vec2 vTextureCoord;
            varying vec2 fTextureCoord;
            uniform vec4 filterArea;
            uniform sampler2D uSampler;
            uniform vec4 glowColor;
            uniform vec2 dimensions;
            uniform vec2 resolution;
            uniform float time;
            uniform vec2 scroll;
            uniform vec3 zoom;
            uniform float intensity;
            uniform float pulse;

            float rand(vec2 coord) {
                float r1 = 15.0 + 60.0;
                float r2 = 15.0 + 60.0;
                float r3 = 4000.0 + 1000.0;
                return fract(sin(dot(coord, vec2(r1, r2))) * r3);
            }

            float noise(vec2 coord) {
                vec2 i = floor(coord);
                vec2 f = fract(coord);
                float a = rand(i);
                float b = rand(i + vec2(1.0, 0.0));
                float c = rand(i + vec2(0.0, 1.0));
                float d = rand(i + vec2(1.0, 1.0));
                vec2 u = f * f * (3.0 - 2.0 * f);
                return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
            }

            float fbm(vec2 coord) {
                float value = 0.0;
                float amplitude = 0.9;
                float frequency = 0.7;
                const int octaves = ${isPerformance ? '1' : '2'};
                
                for (int i = 0; i < octaves; i++) {
                    value += amplitude * noise(coord * 2.25);
                    coord *= 2.0;
                    amplitude *= frequency;
                }
                return value;
            }

            vec4 getFog(float alpha) {
                vec4 fog = vec4(0.0, 0.0, 0.0, 0.0);
                
                if (alpha > 0.0) {
                    vec3 color = glowColor.rgb * (1.0 + glowColor.a * 2.0);
                    float v = time * 10.0;
                    vec2 coord = (vTextureCoord + vec2(0.5)) / (vec2(128.0, 128.0) / 500.0);
                    coord = coord + scroll;
                    
                    vec2 q = vec2(fbm(coord + 0.0 * v), fbm(coord + vec2(1.0)));
                    vec2 r = vec2(
                        fbm(coord + 1.0 * q + vec2(2.7, 7.2) + 0.15 * v),
                        fbm(coord + 1.0 * q + vec2(5.3, 2.8) + 0.126 * v)
                    );
                    
                    float f = fbm(coord / 1.0 + r);
                    float alpha0 = max(0.5 - abs(vTextureCoord.x - 0.4) - 0.3, 0.0);
                    float alpha1 = max(alpha - 0.5, 0.0);
                    vec3 fColor = (color * alpha) * length(vec3(1, 1, 1));
                    
                    fog = vec4((f * f * f + 0.3 * f * f + 0.25 * f) * vec3(1.0, 1.0, 1.0), 0.0);
                    fog.rgb += vec3(alpha1, alpha1, alpha1);
                    fog.rgb += vec3(alpha0, alpha0, alpha0);
                    fog.rgb = fog.rgb * fColor;
                }
                
                fog.a += fog.a;
                return fog;
            }

            float glowAlpha() {
                vec4 centerPixel = texture2D(uSampler, vTextureCoord);
                const float outlineWidthX = ${isPerformance ? '3.0' : '5.0'};
                const float outlineWidthY = ${isPerformance ? '5.0' : '7.0'};
                float totalAlpha = 0.0;
                float min = 0.001;
                float max = 0.025;
                float value = min + glowColor.a * (max - min);
                
                if (centerPixel.a >= 1.0) value = min + 0.006 * 0.01;

                for (float i = -outlineWidthY; i <= outlineWidthY; i++) {
                    for (float j = -outlineWidthX; j <= outlineWidthX; j++) {
                        vec2 offset = vTextureCoord + (vec2(i, j) * value);
                        vec4 surroundingPixel = texture2D(uSampler, offset);
                        if (length(surroundingPixel.rgb) < 0.2) continue;
                        if (surroundingPixel.rgb == vec3(0.0, 0.0, 0.0)) continue;
                        totalAlpha += abs(surroundingPixel.a - centerPixel.a);
                    }
                }
                
                totalAlpha /= ((outlineWidthX * 2.0 + 1.0) * (outlineWidthY * 2.0 + 1.0) * 1.0);
                return totalAlpha;
            }

            void main(void) {
                vec4 texel = texture2D(uSampler, vTextureCoord);
                float alpha = glowAlpha();
                
                if (texel.a >= 0.1) alpha *= 0.6;
                
                // Apply intensity and pulse effects
                alpha *= intensity;
                if (pulse > 0.0) {
                    alpha *= 1.0 + pulse * sin(time * 3.0) * 0.3;
                }
                
                vec4 fog = getFog(alpha);
                
                if (${blendMode} == 0) {
                    gl_FragColor = texel + vec4(fog.rgb * alpha * fog.a, abs(alpha * fog.a));
                } else {
                    gl_FragColor = texel + vec4(fog.rgb * alpha, abs(alpha * fog.a));
                }
            }
        `;
    }

    static get uniforms() {
        if (Utils.RPGMAKER_NAME == 'MV') {
            return {
                glowColor: { type: 'v4', value: [0.0, 0.0, 1.0, 1.0] },
                dimensions: { type: 'v2', value: [0.0, 0.0] },
                scroll: { type: 'v2', value: [0.0, 0.0] },
                zoom: { type: 'v3', value: [0.0, 0.0, 0.0] },
                time: { type: 'f', value: 0.0 },
                intensity: { type: 'f', value: 1.0 },
                pulse: { type: 'f', value: 0.0 }
            };
        } else {
            return {
                glowColor: new Float32Array([1, 0, 0, 1]),
                dimensions: new Float32Array([0, 0]),
                scroll: new Float32Array([0, 0]),
                zoom: new Float32Array([0, 0, 0]),
                time: 0.0,
                intensity: 1.0,
                pulse: 0.0
            };
        }
    }

    constructor(blendMode, performanceMode = 'balanced') {
        const fragSrc = Filter_Aura_Enhanced.fragmentSrc(blendMode, performanceMode);
        super(Filter_Aura_Enhanced.vertexSrc, fragSrc, Filter_Aura_Enhanced.uniforms);
        this.autoFit = false;
        this.performanceMode = performanceMode;
        this.intensity = 1.0;
        this.pulse = 0.0;
        this.pulseSpeed = 1.0;
    }

    reset() {
        this.uniforms.time = 0;
        this.intensity = 1.0;
        this.pulse = 0.0;
        this.pulseSpeed = 1.0;
    }

    setIntensity(value) {
        this.intensity = Math.max(0.1, Math.min(2.0, value));
        this.uniforms.intensity = this.intensity;
    }

    setPulse(enabled, speed = 1.0) {
        this.pulse = enabled ? 1.0 : 0.0;
        this.pulseSpeed = speed;
        this.uniforms.pulse = this.pulse;
    }

    apply(filterManager, input, output, clearMode, _currentState) {
        if (this.uniforms.dimensions) {
            const size = Math.max(input.width, input.height);
            this.uniforms.dimensions[0] = size / 2;
            this.uniforms.dimensions[1] = size;
        }
        super.apply(filterManager, input, output, clearMode, _currentState);
    }

    update() {
        this.uniforms.time += 0.01 * this.pulseSpeed;
    }
}

// Enhanced Aura Manager
class EnhancedAuraManager {
    constructor() {
        this.auras = {};
        this.filterPool = new AuraFilterPool();
        this.performanceMonitor = new AuraPerformanceMonitor();
        this.activeAuras = new Map();
        this.maxAuras = 50;
    }

    createAura(auraData) {
        const aura = this.filterPool.get();
        const performanceMode = auraData.performance || 'balanced';
        
        aura.uniforms.glowColor[0] = auraData.r / 127.5;
        aura.uniforms.glowColor[1] = auraData.g / 127.5;
        aura.uniforms.glowColor[2] = auraData.b / 127.5;
        aura.uniforms.glowColor[3] = auraData.a / 5;
        
        return aura;
    }

    getAura(name) {
        if (!this.auras[name]) {
            const auraData = Chaucer.auraFilter.params.auras.find(a => a.name === name);
            if (auraData) {
                this.auras[name] = this.createAura(auraData);
            }
        }
        return this.auras[name];
    }

    applyAura(sprite, auraName, options = {}) {
        if (this.activeAuras.size >= this.maxAuras) {
            console.warn('Maximum number of active auras reached');
            return false;
        }

        const aura = this.getAura(auraName);
        if (!aura) return false;

        const key = `${sprite.constructor.name}_${sprite._auraId || Date.now()}`;
        
        if (options.intensity) aura.setIntensity(options.intensity);
        if (options.pulse) aura.setPulse(true, options.pulseSpeed || 1.0);
        
        sprite._auraFilter = aura;
        sprite.filters = (sprite.filters || []).concat([aura]);
        
        this.activeAuras.set(key, aura);
        this.performanceMonitor.activeAuras = this.activeAuras.size;
        this.performanceMonitor.maxAuras = Math.max(this.performanceMonitor.maxAuras, this.activeAuras.size);
        
        return true;
    }

    removeAura(sprite) {
        const key = `${sprite.constructor.name}_${sprite._auraId || Date.now()}`;
        const aura = this.activeAuras.get(key);
        
        if (aura) {
            this.filterPool.release(aura);
            this.activeAuras.delete(key);
            this.performanceMonitor.activeAuras = this.activeAuras.size;
        }
        
        if (sprite._auraFilter) {
            sprite.filters = (sprite.filters || []).filter(f => f !== sprite._auraFilter);
            sprite._auraFilter = null;
        }
    }

    update() {
        for (const [key, aura] of this.activeAuras) {
            aura.update();
        }
        this.performanceMonitor.update();
    }

    reset() {
        for (const [key, aura] of this.activeAuras) {
            this.filterPool.release(aura);
        }
        this.activeAuras.clear();
        this.performanceMonitor.activeAuras = 0;
    }
}

//=============================================================================
window.Filter_Aura_Enhanced = Filter_Aura_Enhanced;
//=============================================================================

//=============================================================================
var Imported = Imported || {};
Imported['Enhanced Aura Filter'.toUpperCase()] = true;
//=============================================================================
var Chaucer = Chaucer || {};
Chaucer.auraFilter = Chaucer.auraFilter || {};
//=============================================================================

(function ($) {
    // Enhanced plugin initialization
    $.makePluginInfo = function ($, n) {
        for (var i = 0, l = $plugins.length; i < l; i++) {
            if (!$plugins[i].description.match(n)) continue;

            $.author = 'Chaucer (Enhanced)';
            $.name = RegExp.$1;
            $.version = RegExp.$2;
            $.pluginName = $plugins[i].name;
            $.params = Chaucer.parse($plugins[i].parameters);
            $.commands = {};
            $.alias = {};
            $.auras = {};
            
            // Initialize enhanced aura manager
            $.auraManager = new EnhancedAuraManager();
        }
    };

    // Enhanced plugin commands
    $.registerPluginCommand = function (command, fn) {
        if (Utils.RPGMAKER_NAME === 'MV') $.commands[command] = fn;
        else if (Utils.RPGMAKER_NAME === 'MZ')
            PluginManager.registerCommand($.pluginName, command, fn);
    };

    // Enhanced aura application
    $.applyEnhancedAura = function(sprite, auraName, options = {}) {
        return $.auraManager.applyAura(sprite, auraName, options);
    };

    $.removeEnhancedAura = function(sprite) {
        $.auraManager.removeAura(sprite);
    };

    // Enhanced plugin commands
    $.registerPluginCommand('set_aura_advanced', function (args) {
        const { target, id, aura, intensity, pulse, condition } = args;
        let sprite = null;
        
        switch (target) {
            case 'event':
                const event = $gameMap.event(id);
                if (event) sprite = event._sprite;
                break;
            case 'actor':
                const actor = $gameActors.actor(id);
                if (actor) {
                    // Find actor sprite in battle or map
                    if (SceneManager._scene instanceof Scene_Battle) {
                        const battler = $gameParty.members().find(m => m.actorId() === id);
                        if (battler) sprite = battler._sprite;
                    }
                }
                break;
            case 'enemy':
                if (SceneManager._scene instanceof Scene_Battle) {
                    const enemy = $gameTroop._enemies[id - 1];
                    if (enemy) sprite = enemy._sprite;
                }
                break;
            case 'picture':
                const picture = $gameScreen._pictures[id];
                if (picture) sprite = picture._sprite;
                break;
        }
        
        if (sprite && aura) {
            const options = {
                intensity: parseFloat(intensity) || 1.0,
                pulse: pulse === 'true',
                pulseSpeed: 1.0
            };
            
            if (condition && condition.trim()) {
                try {
                    if (eval(condition)) {
                        $.applyEnhancedAura(sprite, aura, options);
                    }
                } catch (e) {
                    console.error('Aura condition error:', e);
                }
            } else {
                $.applyEnhancedAura(sprite, aura, options);
            }
        }
    });

    $.registerPluginCommand('pulse_aura', function (args) {
        const { target, id, speed } = args;
        let sprite = null;
        
        // Find sprite based on target type
        // (Similar logic as above)
        
        if (sprite && sprite._auraFilter) {
            sprite._auraFilter.setPulse(true, parseFloat(speed) || 1.0);
        }
    });

    $.registerPluginCommand('scale_aura', function (args) {
        const { target, id, maxDistance } = args;
        // Implementation for distance-based scaling
    });

    $.registerPluginCommand('clear_aura_advanced', function (args) {
        const { target, id } = args;
        let sprite = null;
        
        // Find sprite based on target type
        // (Similar logic as above)
        
        if (sprite) {
            $.removeEnhancedAura(sprite);
        }
    });

    // Enhanced sprite updates
    $.alias(Sprite_Character, 'update', function () {
        $.alias();
        this.updateEnhancedAura();
    });

    $.expand(Sprite_Character, 'updateEnhancedAura', function () {
        const character = this._character;
        if (character && this._aura != character.aura()) {
            this.setEnhancedAura(character.aura());
        }
    });

    $.expand(Sprite_Character, 'setEnhancedAura', function (name) {
        if (name && name !== this._aura) {
            $.removeEnhancedAura(this);
            this._aura = name;
            $.applyEnhancedAura(this, name);
        } else if (!name && this._aura) {
            $.removeEnhancedAura(this);
            this._aura = '';
        }
    });

    // Enhanced scene updates
    $.alias(Scene_Map, 'update', function () {
        $.alias();
        $.auraManager.update();
    });

    $.alias(Scene_Battle, 'update', function () {
        $.alias();
        $.auraManager.update();
    });

    // Performance monitoring
    $.alias(Scene_Map, 'terminate', function () {
        $.alias();
        if ($.params.enableDebug) {
            $.auraManager.performanceMonitor.logPerformance();
        }
    });

    $.alias(Scene_Battle, 'terminate', function () {
        $.alias();
        if ($.params.enableDebug) {
            $.auraManager.performanceMonitor.logPerformance();
        }
    });

    //=============================================================================
    // Initialize enhanced plugin
    //=============================================================================
    
    const identifier = /(Enhanced Aura Filter) : Version - (\d+.\d+.\d+)/;
    $.makePluginInfo($, identifier);
    
    if (!$.name) throw new Error('Enhanced Aura Filter was unable to load!');

    //=============================================================================
})(Chaucer.auraFilter);
//============================================================================= 