/*:
 * @plugindesc v2.0 - Enhanced Total Damage Display with Optimized Lightning Effects
 * @version 2.0.0
 * @url
 * @help DisplayTotalDamage2.js
 *
 * Displays total damage with hit counter and realistic lightning visual effects.
 * Optimized for performance with advanced caching and natural lightning simulation.
 *
 * @param duration
 * @text Display Duration
 * @type number
 * @min 1
 * @max 10
 * @desc Duration in seconds for the damage display
 * @default 2
 *
 * @param yPosition
 * @text Y Position
 * @type number
 * @min 0.1
 * @max 0.9
 * @decimals 2
 * @desc Vertical position as a percentage of screen height (0.1 = 10% from top, 0.9 = 90% from top)
 * @default 0.35
 */

(() => {
    'use strict';

    // === PLUGIN CONSTANTS ===
    const PLUGIN_NAME = 'DisplayTotalDamage2';

    // Parse plugin parameters with validation
    const parameters = PluginManager.parameters(PLUGIN_NAME);
    const displayDuration = Math.max(1, parseInt(parameters.duration || 2)) * 60;
    const yPosition = 0.25;
    const baseFontSize = 40;
    const maxDamageCap = 10000;

    // === PERFORMANCE CONSTANTS ===
    const PERFORMANCE = {
        maxFPS: 60,
        minFrameTime: 16, // ~60fps
        lightningUpdateInterval: 2, // Update lightning every 2 frames
        cacheTimeout: 1000, // Cache timeout in ms
        // Adaptive performance scaling
        heavyLoadThreshold: 45, // FPS threshold for heavy load detection
        lightningLOD: {
            high: { segments: { min: 12, max: 24 }, branches: { min: 2, max: 4 } },
            medium: { segments: { min: 8, max: 16 }, branches: { min: 2, max: 3 } },
            low: { segments: { min: 6, max: 12 }, branches: { min: 1, max: 2 } },
        },
        damageGroupingWindow: 100, // ms to group rapid damage
        // Particle performance settings
        particleLOD: {
            high: { maxParticles: 20, spawnRate: 0.8, updateInterval: 1 },
            medium: { maxParticles: 12, spawnRate: 0.6, updateInterval: 2 },
            low: { maxParticles: 6, spawnRate: 0.4, updateInterval: 3 },
        },
    };

    // === MATH OPTIMIZATION CONSTANTS ===
    const MATH_CONSTANTS = {
        PI2: Math.PI * 2,
        PI_HALF: Math.PI / 2,
        PI_QUARTER: Math.PI / 4,
        PI_SIXTH: Math.PI / 6,
        // Pre-calculated timing multipliers
        HEALING_BREATHE_SPEED: 0.006,
        CRIT_PULSE_SPEED: 0.015,
        MILESTONE_PULSE_SPEED: 0.012,
        LIGHTNING_PULSE_SPEED: 0.01,
        HIT_COUNTER_PULSE_SPEED: 0.01,
        TEXT_PULSE_SPEED: 0.008,
        // Common fractions
        ONE_THIRD: 1 / 3,
        TWO_THIRDS: 2 / 3,
        QUARTER: 0.25,
        HALF: 0.5,
        THREE_QUARTERS: 0.75,
    };

    // === STRING OPTIMIZATION ===
    const STRING_CACHE = {
        DAMAGE: 'Damage',
        HEALING: 'Healing',
        HIT_PREFIX: 'Hit x',
        STAR_PREFIX: '★ Hit x',
        STAR_SUFFIX: ' ★',
        LIGHTNING_PREFIX: '⚡ Hit x',
        LIGHTNING_SUFFIX: ' ⚡',
        RGBA_PREFIX: 'rgba(',
        RGBA_SUFFIX: ', 1.0)',
    };

    // === ENHANCED LIGHTNING CONFIG ===
    const LIGHTNING_CONFIG = {
        // Main branch settings (more natural, fewer branches)
        mainBranches: { min: 2, max: 3 },
        branchLength: { min: 60, max: 150 },
        branchAngleSpread: Math.PI / 6, // 30 degrees (more natural spread)

        // Sub-branch settings (more natural branching)
        subBranches: { probability: 0.15, maxPerBranch: 2 }, // Reduced probability, more branches
        subBranchLength: { min: 20, max: 50 },
        subBranchAngle: Math.PI / 3, // 60 degrees (more natural angle)

        // Segment settings (more natural variation)
        segments: { min: 12, max: 24 }, // More segments for more joints
        segmentDeviation: 8, // Less jagged, more flowing
        segmentLengthVariation: 0.3, // Variable segment lengths

        // Visual settings (more dramatic and cool)
        thickness: { base: 3.5, taper: 0.8 }, // Thicker for more impact
        glow: { enabled: true, radius: 12, intensity: 0.5 }, // More intense glow
        flicker: { enabled: true, intensity: 0.3, speed: 0.15 }, // More dramatic flicker

        // Color settings with intensity-based variations
        color: {
            base: { r: 255, g: 255, b: 100 },
            tip: { r: 255, g: 200, b: 255 },
            whiten: 0.3, // Less white for more natural look
            // Intensity-based color themes (no performance cost)
            themes: {
                low: { base: { r: 200, g: 200, b: 255 }, tip: { r: 150, g: 150, b: 255 } }, // Blue for low damage
                medium: { base: { r: 255, g: 255, b: 100 }, tip: { r: 255, g: 200, b: 255 } }, // Yellow (default)
                high: { base: { r: 255, g: 150, b: 50 }, tip: { r: 255, g: 100, b: 100 } }, // Orange-red for high damage
                critical: { base: { r: 255, g: 50, b: 50 }, tip: { r: 255, g: 255, b: 255 } }, // Red-white for critical
            },
        },

        // Animation settings (more natural timing)
        lifetime: 8, // frames (slightly longer for better visibility)
        fadeOut: true,
        
        // Cool lightning behavior
        coolBehavior: {
            zigzagIntensity: 0.6, // More dramatic zigzags
            attractionStrength: 0.5, // Stronger attraction to targets
            branchDecay: 0.8, // Branches last longer
            mainPathBias: 0.7, // Main paths are more prominent
            // Visual effects for cool factor
            electricFieldInfluence: 0.2, // Subtle field influence
            energyDecay: 0.9, // Energy lasts longer for more dramatic effect
            ionizationEffect: 0.3, // More dramatic ionization
            // Cool visual effects
            airResistance: 0.1, // Less resistance for more dramatic movement
            temperatureVariation: 0.4, // More dramatic color variations
                    // Cool factor additions
        pulseIntensity: 0.3, // Pulsing effect
        glowIntensity: 0.5, // More intense glow
        sparkleEffect: 0.2, // Sparkle particles
        // Impact particle settings
        impactParticles: {
            enabled: true,
            maxCount: 15,
            spawnRate: 0.7,
            lifetime: { min: 20, max: 40 },
            speed: { min: 2, max: 6 },
            size: { min: 1, max: 3 },
            fadeRate: 0.95,
        }
        }
    };

    // === UTILITY CLASSES ===

    /**
     * Optimized color utility class with caching
     */
    class ColorUtils {
        static _cache = new Map();

        /**
         * Convert RGBA string to RGB object with caching
         */
        static parseRGBA(rgbaString) {
            if (this._cache.has(rgbaString)) {
                return this._cache.get(rgbaString);
            }

            const match = rgbaString.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
            const result = match
                ? {
                      r: parseInt(match[1]),
                      g: parseInt(match[2]),
                      b: parseInt(match[3]),
                  }
                : { r: 255, g: 255, b: 255 };

            this._cache.set(rgbaString, result);
            return result;
        }

        /**
         * Interpolate between two colors
         */
        static lerp(color1, color2, t) {
            return {
                r: Math.round(color1.r + (color2.r - color1.r) * t),
                g: Math.round(color1.g + (color2.g - color1.g) * t),
                b: Math.round(color1.b + (color2.b - color1.b) * t),
            };
        }

        /**
         * Convert RGB object to RGBA string
         */
        static toRGBA(color, alpha = 1) {
            return `rgba(${color.r},${color.g},${color.b},${alpha})`;
        }

        /**
         * Shift hue of a color (optimized version)
         */
        static shiftHue(rgbColor, degrees) {
            const { r, g, b } = rgbColor;
            const max = Math.max(r, g, b) / 255;
            const min = Math.min(r, g, b) / 255;
            const delta = max - min;

            if (delta === 0) return rgbColor; // Grayscale, no hue to shift

            let h = 0;
            if (max === r / 255) h = ((g / 255 - b / 255) / delta) % 6;
            else if (max === g / 255) h = (b / 255 - r / 255) / delta + 2;
            else h = (r / 255 - g / 255) / delta + 4;

            h = ((h * 60 + degrees + 360) % 360) / 360;
            const s = delta / max;
            const l = (max + min) / 2;

            // Convert back to RGB
            const c = (1 - Math.abs(2 * l - 1)) * s;
            const x = c * (1 - Math.abs(((h * 6) % 2) - 1));
            const m = l - c / 2;

            let rNew, gNew, bNew;
            if (h < 1 / 6) [rNew, gNew, bNew] = [c, x, 0];
            else if (h < 2 / 6) [rNew, gNew, bNew] = [x, c, 0];
            else if (h < 3 / 6) [rNew, gNew, bNew] = [0, c, x];
            else if (h < 4 / 6) [rNew, gNew, bNew] = [0, x, c];
            else if (h < 5 / 6) [rNew, gNew, bNew] = [x, 0, c];
            else [rNew, gNew, bNew] = [c, 0, x];

            return {
                r: Math.round((rNew + m) * 255),
                g: Math.round((gNew + m) * 255),
                b: Math.round((bNew + m) * 255),
            };
        }
    }

    /**
     * Enhanced Performance monitoring utility with adaptive scaling
     */
    class PerformanceMonitor {
        static _frameTime = 0;
        static _lastFrame = 0;
        static _fps = 60;
        static _fpsHistory = [];
        static _heavyLoadMode = false;

        static update() {
            const now = performance.now();
            this._frameTime = now - this._lastFrame;
            this._lastFrame = now;
            this._fps = 1000 / this._frameTime;

            // Track FPS history for adaptive scaling
            this._fpsHistory.push(this._fps);
            if (this._fpsHistory.length > 10) this._fpsHistory.shift();

            // Detect heavy load conditions
            const avgFPS = this._fpsHistory.reduce((a, b) => a + b, 0) / this._fpsHistory.length;
            this._heavyLoadMode = avgFPS < PERFORMANCE.heavyLoadThreshold;
        }

        static shouldSkipFrame(interval = 1) {
            const adaptiveInterval = this._heavyLoadMode ? interval * 2 : interval;
            return (
                Graphics.frameCount %
                    Math.max(1, Math.ceil(adaptiveInterval * (60 / this._fps))) !==
                0
            );
        }

        static getFPS() {
            return this._fps;
        }

        static isHeavyLoad() {
            return this._heavyLoadMode;
        }

        static getLightningLOD() {
            if (this._fps > 50) return PERFORMANCE.lightningLOD.high;
            if (this._fps > 35) return PERFORMANCE.lightningLOD.medium;
            return PERFORMANCE.lightningLOD.low;
        }

        static getParticleLOD() {
            if (this._fps > 50) return PERFORMANCE.particleLOD.high;
            if (this._fps > 35) return PERFORMANCE.particleLOD.medium;
            return PERFORMANCE.particleLOD.low;
        }
    }
    /**
     * Object pool for lightning points to reduce garbage collection
     */
    class ObjectPool {
        static _pointPool = [];
        static _pathPool = [];
        static _particlePool = [];

        static getPoint(x = 0, y = 0) {
            const point = this._pointPool.pop() || { x: 0, y: 0 };
            point.x = x;
            point.y = y;
            return point;
        }

        static releasePoint(point) {
            this._pointPool.push(point);
        }

        static getPath() {
            const path = this._pathPool.pop() || { points: [], thickness: 0 };
            path.points.length = 0;
            path.thickness = 0;
            return path;
        }

        static releasePath(path) {
            // Return points to pool
            for (const point of path.points) {
                this.releasePoint(point);
            }
            path.points.length = 0;
            this._pathPool.push(path);
        }

        static getParticle() {
            const particle = this._particlePool.pop() || {
                x: 0, y: 0, vx: 0, vy: 0, life: 0, maxLife: 0, size: 0, alpha: 1
            };
            return particle;
        }

        static releaseParticle(particle) {
            this._particlePool.push(particle);
        }
    }

    /**
     * Impact Particle System for cool visual effects
     */
    class ImpactParticleSystem {
        static _particles = [];
        static _lastSpawnTime = 0;
        static _spawnCounter = 0;

        /**
         * Spawn impact particles at lightning impact points
         */
        static spawnParticles(x, y, intensity, config) {
            // Ensure impact particles config exists
            if (!config.impactParticles || !config.impactParticles.enabled) return;

            const lodConfig = PerformanceMonitor.getParticleLOD();
            const maxParticles = Math.min(config.impactParticles.maxCount, lodConfig.maxParticles);
            const spawnRate = config.impactParticles.spawnRate * lodConfig.spawnRate;

            // Only spawn particles occasionally for performance
            if (Math.random() > spawnRate) return;

            const particleCount = Math.floor(maxParticles * intensity * 0.5);
            
            for (let i = 0; i < particleCount; i++) {
                const particle = ObjectPool.getParticle();
                
                // Random position around impact point
                const angle = Math.random() * Math.PI * 2;
                const distance = Math.random() * 20;
                particle.x = x + Math.cos(angle) * distance;
                particle.y = y + Math.sin(angle) * distance;
                
                // Random velocity
                const speed = config.impactParticles.speed.min + 
                    Math.random() * (config.impactParticles.speed.max - config.impactParticles.speed.min);
                particle.vx = (Math.random() - 0.5) * speed;
                particle.vy = (Math.random() - 0.5) * speed;
                
                // Random size
                particle.size = config.impactParticles.size.min + 
                    Math.random() * (config.impactParticles.size.max - config.impactParticles.size.min);
                
                // Lifetime
                particle.maxLife = config.impactParticles.lifetime.min + 
                    Math.random() * (config.impactParticles.lifetime.max - config.impactParticles.lifetime.min);
                particle.life = particle.maxLife;
                particle.alpha = 1;
                
                this._particles.push(particle);
            }
        }

        /**
         * Update all particles
         */
        static update() {
            const lodConfig = PerformanceMonitor.getParticleLOD();
            
            // Skip updates based on performance
            if (PerformanceMonitor.shouldSkipFrame(lodConfig.updateInterval)) return;

            for (let i = this._particles.length - 1; i >= 0; i--) {
                const particle = this._particles[i];
                
                // Update position
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                // Update life
                particle.life--;
                particle.alpha = particle.life / particle.maxLife;
                
                // Apply fade rate
                particle.alpha *= LIGHTNING_CONFIG.coolBehavior.impactParticles.fadeRate;
                
                // Remove dead particles
                if (particle.life <= 0 || particle.alpha <= 0.1) {
                    ObjectPool.releaseParticle(particle);
                    this._particles.splice(i, 1);
                }
            }
        }

        /**
         * Render all particles
         */
        static render(ctx, baseColor) {
            if (this._particles.length === 0 || !baseColor) return;

            ctx.save();
            ctx.globalCompositeOperation = 'screen';

            for (const particle of this._particles) {
                if (particle.alpha <= 0.1) continue;

                // Create particle color based on lightning color
                const particleColor = ColorUtils.lerp(baseColor, { r: 255, g: 255, b: 255 }, 0.3);
                
                ctx.fillStyle = ColorUtils.toRGBA(particleColor, particle.alpha);
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
            }

            ctx.restore();
        }

        /**
         * Clear all particles
         */
        static clear() {
            for (const particle of this._particles) {
                ObjectPool.releaseParticle(particle);
            }
            this._particles.length = 0;
        }
    }

    /**
     * Enhanced Lightning Generator with natural branching and object pooling
     */
    class LightningGenerator {
        static _pathCache = new Map();
        static _cacheTimeout = 100; // frames
        static _animationTime = 0; // Global animation time for coordinated effects

        /**
         * Generate realistic lightning paths with branching
         */
        static generatePaths(centerX, centerY, intensity, targetAreas = []) {
            // Update global animation time
            this._animationTime += 0.1;
            
            const cacheKey = `${centerX}_${centerY}_${intensity}_${Math.floor(this._animationTime * 10)}`;

            // Use cached paths if available and recent
            if (this._pathCache.has(cacheKey)) {
                return this._pathCache.get(cacheKey);
            }

            // Use adaptive LOD based on performance
            const lodConfig = PerformanceMonitor.getLightningLOD();
            const config = { 
                ...LIGHTNING_CONFIG, 
                ...lodConfig,
                // Ensure impact particles config is always available
                impactParticles: LIGHTNING_CONFIG.coolBehavior.impactParticles
            };
            const paths = [];

            // Calculate number of main branches based on intensity and performance
            const branchCount = Math.floor(
                config.mainBranches.min + (config.mainBranches.max - config.mainBranches.min) * intensity
            );

            // Generate main branches with coordinated timing
            for (let i = 0; i < branchCount; i++) {
                const angle =
                    (Math.PI * 2 * i) / branchCount +
                    (Math.random() - 0.5) * config.branchAngleSpread;

                const branch = this._generateBranch(
                    centerX,
                    centerY,
                    angle,
                    intensity,
                    config,
                    targetAreas
                );

                if (branch.points.length > 1) {
                    // Add subtle animation offset for each branch
                    branch.animationOffset = i * 0.3;
                    paths.push(branch);

                    // Spawn impact particles at branch endpoints
                    if (branch.points.length > 0) {
                        const lastPoint = branch.points[branch.points.length - 1];
                        ImpactParticleSystem.spawnParticles(lastPoint.x, lastPoint.y, intensity, config);
                    }

                    // Generate sub-branches for more natural look
                    if (Math.random() < config.subBranches.probability) {
                        const subBranches = this._generateSubBranches(branch, config, intensity);
                        paths.push(...subBranches);
                    }
                }
            }

            // Cache the result
            this._pathCache.set(cacheKey, paths);

            // Clean old cache entries
            if (this._pathCache.size > 50) {
                const oldKeys = Array.from(this._pathCache.keys()).slice(0, 10);
                oldKeys.forEach(key => this._pathCache.delete(key));
            }

            return paths;
        }

        /**
         * Generate a single lightning branch with natural zigzag patterns
         */
        static _generateBranch(startX, startY, angle, intensity, config, targetAreas) {
            const points = [ObjectPool.getPoint(startX, startY)];
            const maxLength =
                config.branchLength.min +
                (config.branchLength.max - config.branchLength.min) * intensity;
            const segments = Math.floor(
                config.segments.min + (config.segments.max - config.segments.min) * intensity
            );

            let currentX = startX;
            let currentY = startY;
            let currentAngle = angle;
            let remainingLength = maxLength;
            
            // Cool lightning behavior parameters
            const zigzagIntensity = config.coolBehavior.zigzagIntensity * intensity;
            const attractionStrength = config.coolBehavior.attractionStrength;
            const electricFieldInfluence = config.coolBehavior.electricFieldInfluence;
            const energyDecay = config.coolBehavior.energyDecay;
            const ionizationEffect = config.coolBehavior.ionizationEffect;
            const pulseIntensity = config.coolBehavior.pulseIntensity;
            let zigzagPhase = Math.random() * Math.PI * 2; // Random starting phase
            let currentEnergy = intensity; // Track energy along the path

            for (let i = 0; i < segments && remainingLength > 0; i++) {
                // Variable segment length for more natural appearance
                const baseSegmentLength = remainingLength / (segments - i);
                const lengthVariation = 1 + (Math.random() - 0.5) * config.segmentLengthVariation;
                const segmentLength = Math.min(baseSegmentLength * lengthVariation, maxLength / segments);

                // Cool zigzag pattern (more dramatic lightning)
                const zigzagOffset = Math.sin(zigzagPhase) * zigzagIntensity * 20; // More dramatic movement
                zigzagPhase += Math.PI / 2.5 + (Math.random() - 0.5) * Math.PI / 4; // More varied frequency
                
                // Add cool deviation with momentum (lightning has "memory" of direction)
                const deviation = (Math.random() - 0.5) * config.segmentDeviation * 1.2; // More dramatic deviation
                
                // Electric field influence (lightning follows electric field lines)
                const electricFieldAngle = Math.atan2(0, 1); // Downward electric field
                const fieldInfluence = electricFieldInfluence * currentEnergy;
                currentAngle = this._lerpAngle(currentAngle, electricFieldAngle, fieldInfluence * 0.15);
                
                // Ionization effect (air molecules affect path) - more dramatic
                const ionizationNoise = (Math.random() - 0.5) * ionizationEffect * currentEnergy * 1.5;
                currentAngle += ionizationNoise * 0.08;
                
                // Apply all deviations with more dramatic effect
                currentAngle += deviation * 0.2 + zigzagOffset * 0.03;
                
                // Energy decay along the path (slower for more dramatic effect)
                currentEnergy *= energyDecay;

                // Calculate next point with zigzag
                const zigzagX = Math.cos(currentAngle + zigzagOffset * 0.1) * segmentLength;
                const zigzagY = Math.sin(currentAngle + zigzagOffset * 0.1) * segmentLength;
                
                // Double the X value for wider lightning
                currentX += zigzagX * 2;
                currentY += zigzagY;

                // Keep within screen bounds with natural bounce
                if (currentX < 10 || currentX > Graphics.width - 10) {
                    currentAngle = Math.PI - currentAngle; // Bounce off walls
                    currentX = Math.max(10, Math.min(Graphics.width - 10, currentX));
                }
                if (currentY < 10 || currentY > Graphics.height - 10) {
                    currentAngle = -currentAngle; // Bounce off walls
                    currentY = Math.max(10, Math.min(Graphics.height - 10, currentY));
                }

                points.push(ObjectPool.getPoint(currentX, currentY));
                remainingLength -= segmentLength;

                // Enhanced target attraction with distance-based strength
                if (targetAreas.length > 0 && Math.random() < 0.4) {
                    const nearestTarget = this._findNearestTarget(currentX, currentY, targetAreas);
                    if (nearestTarget) {
                        const distance = Math.sqrt(
                            (nearestTarget.x - currentX) ** 2 + (nearestTarget.y - currentY) ** 2
                        );
                        const maxDistance = 200;
                        const distanceFactor = Math.max(0, 1 - distance / maxDistance);
                        const attractionAngle = Math.atan2(
                            nearestTarget.y - currentY,
                            nearestTarget.x - currentX
                        );
                        const attractionInfluence = attractionStrength * distanceFactor;
                        currentAngle = this._lerpAngle(currentAngle, attractionAngle, attractionInfluence);
                    }
                }
            }

            return {
                points,
                thickness: config.thickness.base * (0.8 + intensity * 0.4),
                intensity: intensity,
                age: 0,
                maxAge: config.lifetime,
                isMainPath: targetAreas.length > 0, // Track if this is a main path
            };
        }

        /**
         * Generate sub-branches from a main branch with natural branching patterns
         */
        static _generateSubBranches(mainBranch, config, intensity) {
            const subBranches = [];
            const maxSubBranches = Math.floor(config.subBranches.maxPerBranch * intensity);

            for (let i = 0; i < maxSubBranches; i++) {
                // Pick a random point along the main branch (avoiding start/end and clustering)
                const avoidStart = 0.2;
                const avoidEnd = 0.2;
                const availableRange = 1 - avoidStart - avoidEnd;
                const basePosition = avoidStart + Math.random() * availableRange;
                
                // Add some clustering - branches tend to form in groups
                const clusterOffset = (Math.random() - 0.5) * 0.15;
                const branchPointIndex = Math.floor(
                    mainBranch.points.length * (basePosition + clusterOffset)
                );
                
                // Ensure valid index
                const validIndex = Math.max(1, Math.min(mainBranch.points.length - 2, branchPointIndex));
                const branchPoint = mainBranch.points[validIndex];

                // Calculate branch angle based on local curve direction (more natural)
                const prevPoint = mainBranch.points[validIndex - 1];
                const nextPoint = mainBranch.points[validIndex + 1];
                const mainAngle = Math.atan2(nextPoint.y - prevPoint.y, nextPoint.x - prevPoint.x);
                
                // Natural branching angles (not always perpendicular)
                const branchDirection = Math.random() < 0.5 ? 1 : -1;
                const baseAngle = Math.PI / 2; // 90 degrees
                const angleVariation = Math.random() * config.subBranchAngle;
                const branchAngle = mainAngle + branchDirection * (baseAngle + angleVariation);

                // Sub-branches should be shorter and less intense
                const subBranchIntensity = intensity * config.coolBehavior.branchDecay;
                const subBranch = this._generateBranch(
                    branchPoint.x,
                    branchPoint.y,
                    branchAngle,
                    subBranchIntensity,
                    {
                        ...config,
                        branchLength: {
                            min: config.subBranchLength.min * 0.8,
                            max: config.subBranchLength.max * 0.9
                        },
                        segments: { min: 6, max: 10 }, // More segments for sub-branches
                        coolBehavior: {
                            ...config.coolBehavior,
                            zigzagIntensity: config.coolBehavior.zigzagIntensity * 0.8, // Still cool but less intense
                            attractionStrength: config.coolBehavior.attractionStrength * 0.6, // Less attraction
                        }
                    },
                    [] // No targets for sub-branches
                );

                if (subBranch.points.length > 1) {
                    // Thinner and more transparent sub-branches
                    subBranch.thickness *= 0.5;
                    subBranch.intensity *= 0.7;
                    subBranch.isMainPath = false;
                    subBranches.push(subBranch);
                }
            }

            return subBranches;
        }

        /**
         * Find nearest target area for lightning attraction
         */
        static _findNearestTarget(x, y, targetAreas) {
            let nearest = null;
            let minDistance = Infinity;

            for (const area of targetAreas) {
                const centerX = area.x + area.width / 2;
                const centerY = area.y + area.height / 2;
                const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = { x: centerX, y: centerY };
                }
            }

            return nearest;
        }

        /**
         * Interpolate between two angles
         */
        static _lerpAngle(angle1, angle2, t) {
            const diff = angle2 - angle1;
            const shortestDiff = ((diff + Math.PI) % (2 * Math.PI)) - Math.PI;
            return angle1 + shortestDiff * t;
        }
    }

    /**
     * Enhanced Lightning Renderer with realistic effects
     */
    class LightningRenderer {
        /**
         * Render lightning paths with advanced visual effects
         */
        static render(ctx, paths, baseColor, intensity) {
            if (!paths || paths.length === 0) return;

            PerformanceMonitor.update();

            const config = LIGHTNING_CONFIG;
            const parsedColor = ColorUtils.parseRGBA(baseColor);

            ctx.save();
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.globalCompositeOperation = 'screen'; // Additive blending for glow

            // Render each path with animation timing
            for (let pathIndex = 0; pathIndex < paths.length; pathIndex++) {
                const path = paths[pathIndex];
                this._renderPath(ctx, path, parsedColor, intensity, config);
            }

            ctx.restore();
        }

        /**
         * Render a single lightning path
         */
        static _renderPath(ctx, path, baseColor, intensity, config) {
            if (path.points.length < 2) return;

            // Apply flicker effect with animation offset
            const animationOffset = path.animationOffset || 0;
            const flickeredPoints = this._applyFlicker(path.points, intensity, animationOffset);

            // Render glow effect first (if enabled)
            if (config.glow.enabled) {
                this._renderGlow(ctx, flickeredPoints, baseColor, path.thickness, config.glow);
            }

            // Render main lightning bolt
            this._renderMainBolt(
                ctx,
                flickeredPoints,
                baseColor,
                path.thickness,
                intensity,
                config
            );
        }

        /**
         * Apply cool flicker to lightning points with dramatic effects
         */
        static _applyFlicker(points, intensity, animationOffset = 0) {
            if (!LIGHTNING_CONFIG.flicker.enabled) return points;

            const flickerIntensity = LIGHTNING_CONFIG.flicker.intensity * intensity;
            const time = LightningGenerator._animationTime + animationOffset;
            const airResistance = LIGHTNING_CONFIG.coolBehavior.airResistance;

            return points.map((point, index) => {
                if (index === 0 || index === points.length - 1) {
                    return point; // Don't flicker start/end points
                }

                // More dramatic flicker with multiple frequencies
                const primaryNoise = Math.sin(time * 3 + index * 0.4) * flickerIntensity * 1.2;
                const secondaryNoise = Math.sin(time * 5.2 + index * 0.8) * flickerIntensity * 0.7;
                const tertiaryNoise = Math.sin(time * 1.8 + index * 1.4) * flickerIntensity * 0.5;
                
                // Cool atmospheric turbulence effect
                const atmosphericNoise = Math.sin(time * 4.1 + index * 1.1) * airResistance * intensity * 1.5;
                
                // Combine multiple noise sources for more dramatic effect
                const combinedNoise = primaryNoise + secondaryNoise + tertiaryNoise + atmosphericNoise;
                
                // Distance-based flicker with energy decay (points further from start flicker more)
                const distanceFactor = index / points.length;
                const energyDecay = LIGHTNING_CONFIG.coolBehavior.energyDecay;
                const energyFactor = Math.pow(energyDecay, index); // Energy decreases along path
                const distanceFlicker = combinedNoise * (0.6 + distanceFactor * 0.6) * energyFactor;
                
                // Add dramatic random variation
                const randomOffset = (Math.random() - 0.5) * flickerIntensity * 12 * energyFactor;
                
                // Apply flicker with cool constraints
                const maxFlicker = 12 * energyFactor; // More dramatic movement
                const flickerX = Math.max(-maxFlicker, Math.min(maxFlicker, distanceFlicker * 5 + randomOffset));
                const flickerY = Math.max(-maxFlicker, Math.min(maxFlicker, distanceFlicker * 3 + randomOffset * 0.4));

                return {
                    x: point.x + flickerX,
                    y: point.y + flickerY,
                };
            });
        }

        /**
         * Render glow effect around lightning
         */
        static _renderGlow(ctx, points, baseColor, thickness, glowConfig) {
            ctx.save();
            ctx.globalAlpha = glowConfig.intensity;
            ctx.shadowColor = ColorUtils.toRGBA(baseColor, 0.8);
            ctx.shadowBlur = glowConfig.radius;
            ctx.strokeStyle = ColorUtils.toRGBA(baseColor, 0.3);
            ctx.lineWidth = thickness * 3;

            this._drawPath(ctx, points);

            ctx.restore();
        }

        /**
         * Render main lightning bolt with cool visual effects
         */
        static _renderMainBolt(ctx, points, baseColor, thickness, intensity, config) {
            const tipColor = ColorUtils.lerp(baseColor, config.color.tip, intensity);

            for (let i = 1; i < points.length; i++) {
                const t = i / (points.length - 1);
                
                // Cool lightning temperature variations (more dramatic)
                const temperatureVariation = config.coolBehavior.temperatureVariation;
                const coreTemperature = 1.2 + Math.sin(t * Math.PI * 1.5) * temperatureVariation; // More dramatic
                const edgeTemperature = 0.6 + Math.sin(t * Math.PI * 2.5) * temperatureVariation * 0.8;
                
                // More dramatic color progression
                const colorVariation = 0.2 + Math.random() * 0.3; // More variation
                const adjustedT = Math.min(1, t + (Math.random() - 0.5) * colorVariation);
                const segmentColor = ColorUtils.lerp(baseColor, tipColor, adjustedT);
                
                // Apply dramatic temperature-based color shifts
                const temperatureShift = this._applyCoolTemperatureShift(segmentColor, coreTemperature);
                const edgeColor = this._applyCoolTemperatureShift(segmentColor, edgeTemperature);
                
                // Add dramatic white core for cool lightning
                const whiteColor = ColorUtils.lerp(
                    temperatureShift,
                    { r: 255, g: 255, b: 255 },
                    config.color.whiten * 1.5 // More white for dramatic effect
                );

                // Cool thickness variation with energy-based taper
                const energyDecay = config.coolBehavior.energyDecay;
                const energyFactor = Math.pow(energyDecay, i); // Energy decreases along path
                const baseThickness = thickness * (1 - t * config.thickness.taper) * energyFactor;
                const thicknessVariation = 0.8 + Math.random() * 0.8; // More dramatic variation
                const segmentThickness = baseThickness * thicknessVariation;

                // Cool alpha fade with energy-based intensity
                const baseAlpha = (1 - t * 0.3) * energyFactor; // Less fade for more dramatic effect
                const alphaVariation = 0.9 + Math.random() * 0.5;
                const alpha = Math.max(0.4, baseAlpha * alphaVariation);

                // Render segment with cool properties
                ctx.strokeStyle = ColorUtils.toRGBA(whiteColor, alpha);
                ctx.lineWidth = segmentThickness;

                ctx.beginPath();
                ctx.moveTo(points[i - 1].x, points[i - 1].y);
                ctx.lineTo(points[i].x, points[i].y);
                ctx.stroke();
            }
        }
        
        /**
         * Apply cool temperature-based color shifts
         */
        static _applyCoolTemperatureShift(color, temperature) {
            // Cool lightning temperature effects (more dramatic colors)
            if (temperature > 1.0) {
                // Hotter - shift toward bright cyan/blue
                return {
                    r: Math.min(255, color.r + (100 - color.r) * (temperature - 1) * 0.4),
                    g: Math.min(255, color.g + (255 - color.g) * (temperature - 1) * 0.6),
                    b: Math.min(255, color.b + (255 - color.b) * (temperature - 1) * 0.8),
                };
            } else {
                // Cooler - shift toward electric purple/magenta
                return {
                    r: Math.min(255, color.r + (255 - color.r) * (1 - temperature) * 0.4),
                    g: Math.max(0, color.g * (0.6 + temperature * 0.4)),
                    b: Math.min(255, color.b + (255 - color.b) * (1 - temperature) * 0.6),
                };
            }
        }

        /**
         * Helper to draw a path through points
         */
        static _drawPath(ctx, points) {
            if (points.length < 2) return;

            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);

            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y);
            }

            ctx.stroke();
        }
    }

    /**
     * Enhanced Total Damage Sprite with optimized lightning effects
     */
    class TotalDamageSprite extends Sprite {
        constructor() {
            super();
            this.initialize.apply(this, arguments);

            // Lightning system
            this._lightningActive = false;
            this._lightningPaths = [];
            this._lightningUpdateTimer = 0;
            this._lastLightningUpdate = 0;
            
            // Particle system
            this._particleUpdateTimer = 0;

            // Performance tracking
            this._lastFrameTime = 0;
            this._frameSkipCounter = 0;
        }

        initialize() {
            super.initialize();

            // Core damage tracking
            this._totalDamage = 0;
            this._displayedDamage = 0;
            this._duration = displayDuration;
            this._isHealing = false;
            this._exploitWeakness = false;
            this._hitCount = 0;

            // Visual effects
            this._lastMilestone = 0;
            this._milestoneEffect = 0;
            this._popEffect = 0;
            this._critFlashTimer = 0;

            // Smart damage grouping for performance
            this._pendingDamage = 0;
            this._lastDamageTime = 0;
            this._damageGroupTimer = 0;

            // Animation
            this._rollStartValue = 0;
            this._rollTargetValue = 0;
            this._rollFrame = 0;
            this._rollDuration = 120;

            // Display properties
            this._displayedBarWidth = 0;
            this._targetBarWidth = 0;

            // Screen tracking
            this._lastWidth = Graphics.width;
            this._lastHeight = Graphics.height;
            this._needsRefresh = true;
            this._lastRefreshTime = 0;

            // Positioning
            this.anchor.x = 0.5;
            this.anchor.y = 0.5;
            this.x = Graphics.width / 2;
            this.y = Graphics.height * yPosition;
            this.opacity = 0;
            this.visible = true;
            this.scale.x = 1.0;
            this.scale.y = 1.0;

            this.createBitmap();
        }

        createBitmap() {
            if (this.bitmap) {
                this.bitmap.destroy();
            }
            const width = Graphics.width;
            const extraLightningPad = 40;
            const verticalPadding = 18 + extraLightningPad;
            const height = 144 + verticalPadding * 2;
            this._verticalPadding = verticalPadding;
            this._extraLightningPad = extraLightningPad;
            this.bitmap = new Bitmap(width, height);
            this.bitmap.fontSize = baseFontSize;
            this.bitmap.fontFace = $gameSystem.mainFontFace();
            this.bitmap.smooth = true;
            // Enhanced caching system
            this._barCache = null;
            this._barCacheParams = { width: 0, height: 0, color: '' };
            this._textCache = null;
            this._textCacheParams = { text: '', fontSize: 0, color: '' };
            this._lastCacheFrame = -1;
        }

        update() {
            super.update();

            // Performance monitoring
            PerformanceMonitor.update();
            const now = performance.now();
            const deltaTime = now - this._lastFrameTime;
            this._lastFrameTime = now;

            // Handle screen resize
            if (this._lastWidth !== Graphics.width || this._lastHeight !== Graphics.height) {
                this._lastWidth = Graphics.width;
                this._lastHeight = Graphics.height;
                this.createBitmap();
                this._needsRefresh = true;
            }

            if (!this.bitmap) {
                this.createBitmap();
                return;
            }

            // Frame rate limiting for performance
            if (deltaTime < PERFORMANCE.minFrameTime) {
                this._frameSkipCounter++;
                if (this._frameSkipCounter < 2) return; // Skip every other frame if running too fast
            }
            this._frameSkipCounter = 0;

            // Process grouped damage
            if (this._damageGroupTimer > 0) {
                this._damageGroupTimer--;
                if (this._damageGroupTimer === 0 && this._pendingDamage > 0) {
                    // Apply grouped damage
                    this._totalDamage += this._pendingDamage;
                    this._pendingDamage = 0;
                    this._lightningActive = true;
                    this._updateLightningPaths();
                }
            }

            // Main update logic
            if (this._duration > 0 || this.opacity > 0) {
                if (this._duration > 0) {
                    this._duration--;
                }

                const diff = this._totalDamage - this._displayedDamage;
                let needsRefresh = false;

                // Optimized roll-up calculation with better performance
                if (Math.abs(diff) > 1) {
                    const step = Math.max(1, Math.ceil(Math.abs(diff) * 0.5)); // Faster roll-up
                    this._displayedDamage += Math.sign(diff) * step;
                    needsRefresh = true;

                    // Check for milestone effects
                    const currentMilestone = Math.floor(this._displayedDamage / 1000) * 1000;
                    if (currentMilestone > this._lastMilestone) {
                        this._lastMilestone = currentMilestone;
                        this._milestoneEffect = 8; // Longer effect
                    }
                } else if (diff !== 0) {
                    this._displayedDamage = this._totalDamage;
                    needsRefresh = true;
                    this._lightningActive = false;
                }

                // Update visual effects
                if (this._milestoneEffect > 0) {
                    this._milestoneEffect--;
                    needsRefresh = true;
                }

                if (this._popEffect > 0) {
                    this._popEffect--;
                    const progress = this._popEffect / 8; // Longer pop effect
                    const scale = 1.0 + 0.15 * Math.sin(progress * Math.PI); // Smoother scaling
                    this.scale.x = scale;
                    this.scale.y = scale;
                } else {
                    this.scale.x = 1.0;
                    this.scale.y = 1.0;
                }

                // Enhanced screen shake for critical hits
                if (this._exploitWeakness) {
                    const shakeIntensity = 2.0;
                    const shakeFreq = Date.now() * 0.01;
                    const shakeX = Math.sin(shakeFreq) * shakeIntensity;
                    const shakeY = Math.cos(shakeFreq * 1.3) * shakeIntensity * 0.5;
                    this.x = Math.round(Graphics.width / 2 + shakeX);
                    this.y = Math.round(Graphics.height * yPosition + shakeY);
                } else {
                    this.x = Math.round(Graphics.width / 2);
                    this.y = Math.round(Graphics.height * yPosition);
                }

                // Smooth fade out
                const fadeStartFrame = 45;
                if (this._duration <= fadeStartFrame) {
                    this.opacity = Math.max(0, Math.floor((this._duration / fadeStartFrame) * 255));
                } else {
                    this.opacity = 255;
                }

                // Ensure final value is shown
                if (this._duration <= 0 && this._displayedDamage !== this._totalDamage) {
                    this._displayedDamage = this._totalDamage;
                    this.refresh();
                }

                if (this._critFlashTimer > 0) {
                    this._critFlashTimer--;
                    needsRefresh = true;
                }

                // Smooth bar width animation
                if (this._targetBarWidth !== undefined) {
                    const diff = this._targetBarWidth - this._displayedBarWidth;
                    if (Math.abs(diff) > 1) {
                        this._displayedBarWidth += diff * 0.2; // Smooth animation
                    } else {
                        this._displayedBarWidth = this._targetBarWidth;
                    }
                }

                // Enhanced lightning system
                const rolling = this._displayedDamage !== this._totalDamage;
                if (rolling && this._totalDamage > 0) {
                    this._lightningActive = true;

                    // Update lightning paths less frequently for performance
                    // Use internal counter instead of Graphics.frameCount for better performance
                    if (!this._lightningUpdateCounter) this._lightningUpdateCounter = 0;
                    this._lightningUpdateCounter++;

                    if (this._lightningUpdateCounter >= PERFORMANCE.lightningUpdateInterval) {
                        this._lightningUpdateCounter = 0;
                        this._updateLightningPaths();
                    }
                } else {
                    this._lightningActive = false;
                    this._lightningPaths = [];
                }

                // Update particle system
                ImpactParticleSystem.update();

                // Intelligent refresh logic
                const shouldRefresh =
                    needsRefresh ||
                    (this._lightningActive && !PerformanceMonitor.shouldSkipFrame(2));

                if (shouldRefresh) {
                    this.refresh();
                }

                this._lastRefreshTime = now;
            } else {
                // Fade out gracefully
                if (this.opacity > 0) {
                    this.opacity = Math.max(0, this.opacity - 12);
                } else {
                    if (this._pendingFade) {
                        this.resetCount();
                    }
                }
            }

            // Reset when fully faded
            if (this._pendingReset && this.opacity <= 0) {
                this.resetCount();
                this._pendingReset = false;
            }

            // Clean up when invisible
            if (this.opacity <= 0) {
                this._lightningPaths = [];
            }
        }

        /**
         * Update lightning paths for current damage state
         */
        _updateLightningPaths() {
            if (!this._lightningActive || this._totalDamage <= 0) {
                this._lightningPaths = [];
                return;
            }

            const intensity = Math.min(1, this._totalDamage / maxDamageCap);
            // Center lightning within the damage display rectangle
            const centerX = this.bitmap.width / 2;
            const padY = this._verticalPadding || 0;
            const centerY = padY + 42; // Center within the text/bar area

            // Lightning will use gradientBorderColor for consistency

            // Define target areas for lightning attraction (bitmap-relative)
            const targetAreas = [];
            if (this._displayedBarWidth > 0) {
                const barX = (this.bitmap.width - this._displayedBarWidth) / 2;
                const barY = padY;
                targetAreas.push({
                    x: barX,
                    y: barY,
                    width: this._displayedBarWidth,
                    height: 60,
                });
            }

            // Clean up old paths before generating new ones
            if (this._lightningPaths) {
                for (const path of this._lightningPaths) {
                    ObjectPool.releasePath(path);
                }
            }

            // Generate new lightning paths
            this._lightningPaths = LightningGenerator.generatePaths(
                centerX,
                centerY,
                intensity,
                targetAreas
            );
        }

        /**
         * Get lightning color theme based on damage intensity and type
         */
        _getLightningColorTheme(intensity) {
            const themes = LIGHTNING_CONFIG.color.themes;

            if (this._exploitWeakness) {
                return themes.critical;
            } else if (intensity > 0.8) {
                return themes.high;
            } else if (intensity > 0.4) {
                return themes.medium;
            } else {
                return themes.low;
            }
        }

        /**
         * Smart damage grouping for performance during heavy action
         */
        _shouldGroupDamage(damage) {
            const now = Date.now();
            const timeSinceLastDamage = now - this._lastDamageTime;

            // Group damage if it's within the grouping window and we're in heavy load
            return (
                timeSinceLastDamage < PERFORMANCE.damageGroupingWindow &&
                PerformanceMonitor.isHeavyLoad() &&
                Math.abs(damage) < 1000
            ); // Don't group large damage numbers
        }

        /**
         * Add damage to the display with enhanced effects and smart grouping
         */
        addDamage(damage, isHealing = false, exploitWeakness = false) {
            if (!this.bitmap) {
                this.createBitmap();
            }

            const absValue = Math.abs(damage);
            if (absValue === 0) return;

            const now = Date.now();

            // Smart damage grouping during heavy load
            if (this._shouldGroupDamage(damage) && !exploitWeakness) {
                this._pendingDamage += absValue;
                this._damageGroupTimer = 5; // Frames to wait before applying
                this._lastDamageTime = now;
                return;
            }

            // Apply any pending grouped damage
            const totalDamage = absValue + this._pendingDamage;
            this._pendingDamage = 0;
            this._lastDamageTime = now;

            // Update damage state
            this._isHealing = isHealing;
            this._exploitWeakness = exploitWeakness;
            this._totalDamage += totalDamage;
            this._hitCount = Math.max(1, this._hitCount + 1);
            this._duration = displayDuration;
            this.opacity = 255;
            this.y = Graphics.height * yPosition; // Use plugin parameter instead of hardcoded value
            this._needsRefresh = true;

            // Enhanced visual effects
            this._popEffect = 8; // Longer pop effect

            if (this._exploitWeakness) {
                this._critFlashTimer = 20; // Longer flash for critical hits
            }

            // Update roll animation
            if (this._rollTargetValue !== this._totalDamage) {
                this._rollStartValue = this._displayedDamage;
                this._rollTargetValue = this._totalDamage;
                this._rollFrame = 0;
            }

            // Instant display for small damage
            if (absValue < 10) {
                this._displayedDamage = this._totalDamage;
            } else if (this._totalDamage > 0) {
                this._displayedDamage = this._displayedDamage || 0;
            }

            // Trigger lightning effect
            this._lightningActive = true;
            this._updateLightningPaths();
        }

        resetCount() {
            this._totalDamage = 0;
            this._displayedDamage = 0;
            this._hitCount = 0;
            this._isHealing = false;
            this._exploitWeakness = false;
            this._lastMilestone = 0;
            this._milestoneEffect = 0;
            this._popEffect = 0;
            this.scale.x = 1.0;
            this.scale.y = 1.0;
            
            // Clear particles when resetting
            ImpactParticleSystem.clear();
        }

        getFontSize() {
            const scaleFactor = Math.min(1 + this._totalDamage / maxDamageCap, 1.3);
            return Math.floor(baseFontSize * scaleFactor);
        }

        refresh() {
            // Early exits for maximum performance
            if (!this.bitmap) return;
            if (this._totalDamage === 0 && this._hitCount === 0) return;

            // Skip refresh if nothing changed and not in active animation
            const currentFrame = Graphics.frameCount;
            if (
                currentFrame === this._lastCacheFrame &&
                !this._lightningActive &&
                this._milestoneEffect === 0 &&
                this._popEffect === 0 &&
                this._critFlashTimer === 0
            ) {
                return;
            }
            this._lastCacheFrame = currentFrame;

            this.bitmap.clear();

            const damageValue = Math.round(this._displayedDamage).toLocaleString();
            // Enhanced hit counter with streak indicators (optimized string building)
            let hitText = '';
            if (this._hitCount > 0) {
                if (this._hitCount >= 10) {
                    hitText =
                        STRING_CACHE.LIGHTNING_PREFIX +
                        this._hitCount +
                        STRING_CACHE.LIGHTNING_SUFFIX;
                } else if (this._hitCount >= 5) {
                    hitText = STRING_CACHE.STAR_PREFIX + this._hitCount + STRING_CACHE.STAR_SUFFIX;
                } else {
                    hitText = STRING_CACHE.HIT_PREFIX + this._hitCount;
                }
            }
            const scale = Math.min(Math.max(this._totalDamage / 2000, 1), 1.3);
            const mainFontSize = Math.round(baseFontSize * scale);
            const subFontSize = Math.round(baseFontSize * 0.75);
            this.bitmap.fontSize = mainFontSize;
            const valueWidth = this.bitmap.measureTextWidth(damageValue);
            const valueHeight = this.bitmap.fontSize;
            this.bitmap.fontSize = subFontSize;
            const labelText = this._isHealing ? STRING_CACHE.HEALING : STRING_CACHE.DAMAGE;
            const labelWidth = this.bitmap.measureTextWidth(labelText);
            const spacing = 12;
            const totalWidth = valueWidth + spacing + labelWidth;
            const startX = (Graphics.width - totalWidth) / 2;
            const padY = this._verticalPadding || 0;

            if (this._totalDamage > 0) {
                const barPaddingX = 36;
                const barPaddingY = 6;
                const barWidth = totalWidth + barPaddingX * 2;
                this._targetBarWidth = barWidth;
                if (!this._displayedBarWidth || this._displayedBarWidth === 0)
                    this._displayedBarWidth = barWidth;
                const barHeight = valueHeight + subFontSize + barPaddingY * 2;
                const barX = (Graphics.width - this._displayedBarWidth) / 2;
                const barY = padY;
                const ctx = this.bitmap.context;
                let barColor = 'rgba(0,0,0,0.4)';
                let pulseIntensity = 1.0;

                // Dynamic bar color with pulsing effect for maximum visual punch
                if (this._isHealing) {
                    barColor = 'rgba(40,120,40,0.4)';
                    pulseIntensity = 1.0 + Math.sin(Date.now() * 0.008) * 0.2; // Gentle healing pulse
                } else if (this._exploitWeakness) {
                    barColor = 'rgba(200,40,40,0.4)';
                    pulseIntensity = 1.0 + Math.sin(Date.now() * 0.015) * 0.4; // Intense critical pulse
                } else if (this._milestoneEffect > 0) {
                    const border = this.gradientBorderColor();
                    const rgb = border.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                    if (rgb) {
                        const r = Math.floor(parseInt(rgb[1]) * 0.5);
                        const g = Math.floor(parseInt(rgb[2]) * 0.5);
                        const b = Math.floor(parseInt(rgb[3]) * 0.5);
                        barColor = `rgba(${r},${g},${b},0.4)`;
                    }
                    pulseIntensity = 1.0 + Math.sin(Date.now() * 0.012) * 0.3; // Milestone celebration pulse
                } else if (this._lightningActive) {
                    // Synchronized pulse with lightning and text effects
                    const syncTime = Date.now() * 0.01;
                    pulseIntensity = 1.0 + Math.sin(syncTime) * 0.1;
                }

                // Apply pulse intensity to bar opacity (zero performance cost)
                const baseOpacity = 0.4;
                const pulsedOpacity = Math.min(0.8, baseOpacity * pulseIntensity);
                barColor = barColor.replace('0.4)', `${pulsedOpacity.toFixed(2)})`);
                // --- Optimized solid color bar with caching ---
                if (
                    !this._barCache ||
                    this._barCacheParams.width !== this._displayedBarWidth ||
                    this._barCacheParams.height !== barHeight ||
                    this._barCacheParams.color !== barColor
                ) {
                    // Create or update the offscreen canvas
                    this._barCache = document.createElement('canvas');
                    this._barCache.width = this._displayedBarWidth;
                    this._barCache.height = barHeight;
                    const barCtx = this._barCache.getContext('2d');
                    const radius = 12;
                    barCtx.beginPath();
                    barCtx.moveTo(radius, 0);
                    barCtx.lineTo(this._displayedBarWidth - radius, 0);
                    barCtx.quadraticCurveTo(
                        this._displayedBarWidth,
                        0,
                        this._displayedBarWidth,
                        radius
                    );
                    barCtx.lineTo(this._displayedBarWidth, barHeight - radius);
                    barCtx.quadraticCurveTo(
                        this._displayedBarWidth,
                        barHeight,
                        this._displayedBarWidth - radius,
                        barHeight
                    );
                    barCtx.lineTo(radius, barHeight);
                    barCtx.quadraticCurveTo(0, barHeight, 0, barHeight - radius);
                    barCtx.lineTo(0, radius);
                    barCtx.quadraticCurveTo(0, 0, radius, 0);
                    barCtx.closePath();
                    barCtx.fillStyle = barColor;
                    barCtx.fill();
                    // Add 1px solid border using gradientBorderColor
                    barCtx.strokeStyle = this.gradientBorderColor();
                    barCtx.lineWidth = 1;
                    barCtx.stroke();
                    this._barCacheParams = {
                        width: this._displayedBarWidth,
                        height: barHeight,
                        color: barColor,
                    };
                }
                // Blit the cached bar to the main context
                ctx.drawImage(this._barCache, barX, barY);
                // --- End optimized solid color bar ---
                // --- Bar border flash sync with lightning ---
                let barBorderColor = this.gradientBorderColor();
                // Make the border darker by default
                const rgbDark = barBorderColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                if (rgbDark) {
                    const r = Math.floor(parseInt(rgbDark[1]) * 0.5);
                    const g = Math.floor(parseInt(rgbDark[2]) * 0.5);
                    const b = Math.floor(parseInt(rgbDark[3]) * 0.5);
                    barBorderColor = `rgba(${r},${g},${b},1)`;
                }
                if (this._lightningActive && this._borderFlashActive) {
                    // Flash: border is bright when lightning is redrawn
                    const rgb = this.gradientBorderColor().match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                    if (rgb) {
                        const r = Math.min(255, Math.floor(parseInt(rgb[1]) * 1.8 + 100));
                        const g = Math.min(255, Math.floor(parseInt(rgb[2]) * 1.8 + 100));
                        const b = Math.min(255, Math.floor(parseInt(rgb[3]) * 1.8 + 100));
                        barBorderColor = `rgba(${r},${g},${b},1)`;
                    }
                }
                // Draw the bar border (1px solid)
                ctx.save();
                ctx.strokeStyle = barBorderColor;
                ctx.lineWidth = 1;
                const radius = 12;
                ctx.beginPath();
                ctx.moveTo(barX + radius, barY);
                ctx.lineTo(barX + this._displayedBarWidth - radius, barY);
                ctx.quadraticCurveTo(
                    barX + this._displayedBarWidth,
                    barY,
                    barX + this._displayedBarWidth,
                    barY + radius
                );
                ctx.lineTo(barX + this._displayedBarWidth, barY + barHeight - radius);
                ctx.quadraticCurveTo(
                    barX + this._displayedBarWidth,
                    barY + barHeight,
                    barX + this._displayedBarWidth - radius,
                    barY + barHeight
                );
                ctx.lineTo(barX + radius, barY + barHeight);
                ctx.quadraticCurveTo(barX, barY + barHeight, barX, barY + barHeight - radius);
                ctx.lineTo(barX, barY + radius);
                ctx.quadraticCurveTo(barX, barY, barX + radius, barY);
                ctx.closePath();
                ctx.stroke();
                ctx.restore();
                // --- End bar border flash ---
                // --- Enhanced Lightning Rendering ---
                if (this._lightningActive && this._lightningPaths.length > 0) {
                    const intensity = Math.min(1, this._totalDamage / maxDamageCap);

                    // Use gradient border color for lightning to match damage display
                    const lightningColor = this.gradientBorderColor();

                    // Render lightning with consistent color system
                    LightningRenderer.render(ctx, this._lightningPaths, lightningColor, intensity);

                    // Render impact particles
                    ImpactParticleSystem.render(ctx, ColorUtils.parseRGBA(lightningColor));

                    this._borderFlashActive = true;
                } else {
                    this._borderFlashActive = false;
                }

                // --- End Enhanced Lightning ---
                // Draw main text
                this.bitmap.fontSize = mainFontSize;
                this.drawOutlinedText(damageValue, startX, padY, valueWidth, valueHeight, 'left');
                // Draw label
                this.bitmap.fontSize = subFontSize;
                const labelY = padY + valueHeight - subFontSize - 3;
                this.drawOutlinedText(
                    labelText,
                    startX + valueWidth + spacing,
                    labelY,
                    labelWidth,
                    subFontSize,
                    'left'
                );
            }
            // Draw hit counter with streak color coding
            if (this._hitCount > 0) {
                this.bitmap.fontSize = subFontSize;
                const hitWidth = this.bitmap.measureTextWidth(hitText);
                const hitY = padY + valueHeight + 6;

                // Get hit counter color based on streak
                const hitColor = this._getHitCounterColor();

                this.drawOutlinedTextWithColor(
                    hitText,
                    (Graphics.width - hitWidth) / 2,
                    hitY,
                    hitWidth,
                    subFontSize,
                    'center',
                    hitColor
                );
            }
        }

        /**
         * Get hit counter color based on streak level (zero performance cost)
         */
        _getHitCounterColor() {
            if (this._hitCount >= 10) {
                // High streak - pulsing gold (optimized)
                const now = Date.now();
                const pulse = 1.0 + Math.sin(now * MATH_CONSTANTS.HIT_COUNTER_PULSE_SPEED) * 0.2;
                const intensity = Math.floor(255 * pulse);
                const goldIntensity = Math.floor(intensity * 0.8);
                return (
                    STRING_CACHE.RGBA_PREFIX +
                    Math.min(255, intensity) +
                    ', ' +
                    goldIntensity +
                    ', 0' +
                    STRING_CACHE.RGBA_SUFFIX
                );
            } else if (this._hitCount >= 5) {
                // Medium streak - bright cyan
                return 'rgba(100, 255, 255, 1.0)';
            } else if (this._hitCount >= 3) {
                // Small streak - light blue
                return 'rgba(180, 220, 255, 1.0)';
            } else {
                // Normal - white
                return 'rgba(255, 255, 255, 1.0)';
            }
        }

        /**
         * Enhanced drawOutlinedText with custom color support
         */
        drawOutlinedTextWithColor(text, x, y, maxWidth, maxHeight, align, customColor = null) {
            const originalTextColor = this.bitmap.textColor;
            if (customColor) {
                this.bitmap.textColor = customColor;
            }
            this.drawOutlinedText(text, x, y, maxWidth, maxHeight, align);
            this.bitmap.textColor = originalTextColor;
        }

        drawOutlinedText(text, x, y, maxWidth, maxHeight, align) {
            // Use the same flash logic as the border for the color outline
            let colorOutline;
            if (this._borderFlashActive) {
                // Bright flash color
                const rgb = this.gradientBorderColor().match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                if (rgb) {
                    const r = Math.min(255, Math.floor(parseInt(rgb[1]) * 1.8 + 100));
                    const g = Math.min(255, Math.floor(parseInt(rgb[2]) * 1.8 + 100));
                    const b = Math.min(255, Math.floor(parseInt(rgb[3]) * 1.8 + 100));
                    colorOutline = `rgba(${r},${g},${b},1)`;
                } else {
                    colorOutline = this.gradientBorderColor();
                }
            } else {
                // Dark color
                const rgbDark = this.gradientBorderColor().match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                if (rgbDark) {
                    const r = Math.floor(parseInt(rgbDark[1]) * 0.5);
                    const g = Math.floor(parseInt(rgbDark[2]) * 0.5);
                    const b = Math.floor(parseInt(rgbDark[3]) * 0.5);
                    colorOutline = `rgba(${r},${g},${b},1)`;
                } else {
                    colorOutline = this.gradientBorderColor();
                }
            }
            // Draw colored outline (5px)
            this.bitmap.outlineWidth = 5;
            this.bitmap.outlineColor = colorOutline;
            this.bitmap.textColor = this.textColor();
            this.bitmap.drawText(text, x, y, maxWidth, maxHeight, align);
            // Draw black outline (3px)
            this.bitmap.outlineWidth = 3;
            this.bitmap.outlineColor = 'rgba(0, 0, 0, 1.0)';
            this.bitmap.drawText(text, x, y, maxWidth, maxHeight, align);
            // Draw filled text
            this.bitmap.outlineWidth = 0;
            this.bitmap.drawText(text, x, y, maxWidth, maxHeight, align);
        }

        textColor() {
            if (this._isHealing) {
                // Healing with subtle breathing effect using pre-calculated constant
                const breathe =
                    1.0 + Math.sin(Date.now() * MATH_CONSTANTS.HEALING_BREATHE_SPEED) * 0.1;
                const green = Math.floor(255 * breathe);
                return `rgba(176, ${Math.min(255, green)}, 144, 1.0)`;
            } else if (this._critFlashTimer > 0) {
                // Enhanced critical flash with multiple color phases
                const flashPhase = this._critFlashTimer / 20;
                if (flashPhase > 0.7) {
                    return 'rgba(255, 255, 255, 1.0)'; // White flash peak
                } else if (flashPhase > 0.4) {
                    return 'rgba(255, 230, 64, 1.0)'; // Yellow flash
                } else {
                    return 'rgba(255, 180, 100, 1.0)'; // Orange fade
                }
            } else {
                // Dynamic color based on damage intensity with smooth transitions
                const intensity = Math.min(1, this._totalDamage / maxDamageCap);
                if (intensity >= 0.8) {
                    // High damage - pulsing red
                    const pulse =
                        1.0 + Math.sin(Date.now() * MATH_CONSTANTS.TEXT_PULSE_SPEED) * 0.15;
                    const red = Math.floor(255 * pulse);
                    return `rgba(${Math.min(255, red)}, 200, 200, 1.0)`;
                } else if (intensity >= 0.5) {
                    // Medium-high damage - warm orange
                    return 'rgba(255, 220, 180, 1.0)';
                } else if (intensity >= 0.2) {
                    // Medium damage - soft yellow
                    return 'rgba(255, 255, 220, 1.0)';
                } else {
                    // Low damage - cool white
                    return 'rgba(240, 245, 255, 1.0)';
                }
            }
        }

        gradientBorderColor() {
            const progress = Math.min(this._totalDamage / maxDamageCap, 1);
            const red = progress < 0.5 ? Math.floor(510 * progress) : 255;
            const green = progress < 0.5 ? 255 : Math.floor(255 - 510 * (progress - 0.5));

            // Enhanced magical pulsing effect
            if (this._exploitWeakness || this._milestoneEffect > 0) {
                const basePulse = Math.sin(Date.now() / 100) * 0.2 + 0.8; // Increased pulse range
                let totalBoost = 0;

                if (this._exploitWeakness) totalBoost += 0.2;
                if (this._milestoneEffect > 0) totalBoost += 0.2;

                // Cap the maximum boost
                totalBoost = Math.min(totalBoost, 0.3);

                return `rgba(${red},${green},0,${basePulse + totalBoost})`;
            }
            return `rgba(${red},${green},0,0.9)`; // Increased base opacity
        }

        /**
         * Enhanced destroy method with proper cleanup
         */

        destroy() {
            // Clean up bitmap
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }

            // Clean up cached resources
            if (this._barCache) {
                this._barCache = null;
            }

            // Clear lightning paths
            this._lightningPaths = [];

            // Clear particles
            ImpactParticleSystem.clear();

            // Remove from parent
            if (this.parent) {
                this.parent.removeChild(this);
            }
        }
    }

    const _Scene_Battle_createAllWindows = Scene_Battle.prototype.createAllWindows;
    Scene_Battle.prototype.createAllWindows = function () {
        _Scene_Battle_createAllWindows.call(this);
        this.createTotalDamageWindow();
    };

    Scene_Battle.prototype.createTotalDamageWindow = function () {
        this._totalDamageSprite = new TotalDamageSprite();
        this.addChild(this._totalDamageSprite);
    };

    const _Game_Action_executeHpDamage = Game_Action.prototype.executeHpDamage;
    Game_Action.prototype.executeHpDamage = function (target, value) {
        _Game_Action_executeHpDamage.call(this, target, value);
        if (SceneManager._scene instanceof Scene_Battle) {
            const isHealing = value < 0;
            const exploitWeakness =
                this.isDrain() || this.item().damage.elementId < 0
                    ? false
                    : target.result().critical || this.calcElementRate(target) > 1;
            SceneManager._scene._totalDamageSprite.addDamage(value, isHealing, exploitWeakness);
        }
    };

    const _BattleManager_startAction = BattleManager.startAction;
    BattleManager.startAction = function () {
        _BattleManager_startAction.call(this);
        if (SceneManager._scene instanceof Scene_Battle && SceneManager._scene._totalDamageSprite) {
            SceneManager._scene._totalDamageSprite.resetCount();
        }
    };
})();
