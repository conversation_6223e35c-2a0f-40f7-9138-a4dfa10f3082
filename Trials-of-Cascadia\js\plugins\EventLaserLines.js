//=============================================================================
// EventLaserLines.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc Creates animated laser beams that emit from single events
 * <AUTHOR> Name
 * @version 1.0.0
 * 
 * @param laserColor
 * @text Laser Color
 * @desc The color of the laser lines (hex format).
 * @default #FF0066
 * @type string
 * 
 * @param laserWidth
 * @text Laser Width
 * @desc The width of the laser lines in pixels.
 * @default 3
 * @type number
 * @min 1
 * @max 10
 * 
 * @param laserOpacity
 * @text Laser Opacity
 * @desc The opacity of the laser lines (0-255).
 * @default 180
 * @type number
 * @min 50
 * @max 255
 * 
 * @param laserPulse
 * @text Laser Pulse
 * @desc Whether the laser should pulse/flicker.
 * @default true
 * @type boolean
 * 
 * @param laserPulseSpeed
 * @text Pulse Speed
 * @desc How fast the laser pulses (higher = faster).
 * @default 0.1
 * @type number
 * @min 0.01
 * @max 0.5
 * @decimals 2
 * 
 * @param laserGlow
 * @text Laser Glow
 * @desc Whether to add a glow effect around the laser.
 * @default true
 * @type boolean
 * 
 * @param laserGlowSize
 * @text Glow Size
 * @desc The size of the glow effect around the laser.
 * @default 8
 * @type number
 * @min 0
 * @max 20
 * 
 * @param laserFloat
 * @text Laser Float
 * @desc Whether the laser should have a floating animation.
 * @default true
 * @type boolean
 * 
 * @param laserParticles
 * @text Laser Particles
 * @desc Whether to add animated particles along the laser.
 * @default true
 * @type boolean
 * 
 * @param laserFlicker
 * @text Laser Flicker
 * @desc Whether the laser should have a dangerous flicker effect.
 * @default true
 * @type boolean
 * 
 * @param laserFlickerIntensity
 * @text Flicker Intensity
 * @desc How intense the flicker effect is (0.1-0.8).
 * @default 0.3
 * @type number
 * @min 0.1
 * @max 0.8
 * @decimals 1
 * 
 * @param laserHeatDistortion
 * @text Heat Distortion
 * @desc Whether to add heat distortion effects to the laser.
 * @default true
 * @type boolean
 * 
 * @param laserElectricArcs
 * @text Electric Arcs
 * @desc Whether to add dangerous electric arc effects along the laser.
 * @default true
 * @type boolean
 * 
 * @param laserWarningPulse
 * @text Warning Pulse
 * @desc Whether the laser should flash warning red colors.
 * @default true
 * @type boolean
 * 
 * @help
 * ============================================================================
 * Event Laser Lines Plugin
 * ============================================================================
 * 
 * This plugin creates animated laser beams that emit from single events
 * 
 * ============================================================================
 * Setup Instructions
 * ============================================================================
 * 
 * 1. Place an event where you want the laser to emit from
 * 2. Add a notetag to the event:
 * 
 * Basic laser:
 * <laser>
 * 
 * Custom laser:
 * <laser:direction=right,length=100,color=#00FFFF>
 * 
 * Dangerous laser with all effects:
 * <laser:direction=up,length=120,color=#FF0000,flicker=true,electricArcs=true,warningPulse=true>
 * 
 * Multiple lasers on same event:
 * <laser 1>
 * <laser 2:direction=up,length=80>
 * 
 * ============================================================================
 * Notetag Examples
 * ============================================================================
 * 
 * Basic laser:
 * <laser>
 * 
 * Directional laser:
 * <laser:direction=right,length=120>
 * <laser:direction=up,length=80>
 * <laser:direction=left,length=100>
 * <laser:direction=down,length=60>
 * 
 * Custom laser properties:
 * <laser:direction=right,length=100,color=#00FFFF,width=5,pulse=false>
 * 
 * Multiple lasers:
 * <laser 1:direction=right,length=100>
 * <laser 2:direction=up,length=80>
 * <laser 3:direction=left,length=120>
 * 
 * ============================================================================
 * Plugin Parameters
 * ============================================================================
 * 
 * - laserColor: Base color of all lasers (can be overridden per laser)
 * - laserWidth: Default width of laser lines
 * - laserOpacity: Default opacity of laser lines
 * - laserPulse: Whether lasers pulse by default
 * - laserPulseSpeed: Speed of the pulse effect
 * - laserGlow: Whether to add glow effects
 * - laserGlowSize: Size of glow effects
 * 
 * ============================================================================
 * Version History
 * ============================================================================
 * 
 * v1.0.0 - Initial release
 * 
 * ============================================================================
 */

(() => {
    'use strict';

    //=============================================================================
    // Plugin Parameters
    //=============================================================================
    const parameters = PluginManager.parameters('EventLaserLines');
    const LASER_COLOR = parameters['laserColor'] || '#FF0066';
    const LASER_WIDTH = Number(parameters['laserWidth']) || 3;
    const LASER_OPACITY = Number(parameters['laserOpacity']) || 180;
    const LASER_PULSE = parameters['laserPulse'] === 'true';
    const LASER_PULSE_SPEED = Number(parameters['laserPulseSpeed']) || 0.1;
    const LASER_GLOW = parameters['laserGlow'] === 'true';
    const LASER_GLOW_SIZE = Number(parameters['laserGlowSize']) || 8;
    const LASER_FLOAT = parameters['laserFloat'] === 'true';
    const LASER_PARTICLES = parameters['laserParticles'] === 'true';
    const LASER_FLICKER = parameters['laserFlicker'] === 'true';
    const LASER_FLICKER_INTENSITY = Number(parameters['laserFlickerIntensity']) || 0.3;
    const LASER_HEAT_DISTORTION = parameters['laserHeatDistortion'] === 'true';
    const LASER_ELECTRIC_ARCS = parameters['laserElectricArcs'] === 'true';
    const LASER_WARNING_PULSE = parameters['laserWarningPulse'] === 'true';

    //=============================================================================
    // Laser Line Sprite
    //=============================================================================
    class LaserLine extends Sprite {
        constructor(x, y, direction, length, settings) {
            super();
            this.x = x;
            this.y = y;
            this.originalY = y; // Store original Y for floating animation
            this.direction = direction;
            this.length = length;
            this.settings = settings;
            this.pulseTime = 0;
            this.glowTime = 0;
            this.floatTime = 0;
            this.heatTime = 0;
            this.flickerTime = 0;
            this.arcTime = 0;
            this.warningTime = 0;
            
            this.createLaserBitmap();
        }

        createLaserBitmap() {
            // Get direction angle
            const directionAngles = {
                'right': 0,
                'up': -Math.PI / 2,
                'left': Math.PI,
                'down': Math.PI / 2
            };
            
            const angle = directionAngles[this.direction] || 0;
            const length = Math.max(this.length, 10);
            const height = Math.max(this.settings.width || LASER_WIDTH, 3);
            
            // Create bitmap with extra space for glow
            const extraSpace = this.settings.glow ? this.settings.glowSize * 2 : 0;
            const width = length + extraSpace;
            const bitmapHeight = height + extraSpace;
            
            const bitmap = new Bitmap(width, bitmapHeight);
            const ctx = bitmap.context;
            
            // Center the drawing context
            ctx.translate(extraSpace / 2, bitmapHeight / 2);
            
            // Apply rotation
            ctx.rotate(angle);
            
            // Create laser line
            this.drawLaserLine(ctx, length);
            
            // Add glow effect if enabled
            if (this.settings.glow) {
                this.drawLaserGlow(ctx, length);
            }
            
            this.bitmap = bitmap;
            this.anchor.x = 0.5;
            this.anchor.y = 0.5;
            
            // Ensure sprite has proper dimensions
            this.width = width;
            this.height = bitmapHeight;
        }

        drawLaserLine(ctx, length) {
            const width = this.settings.width || LASER_WIDTH;
            const color = this.settings.color || LASER_COLOR;
            const opacity = this.settings.opacity || LASER_OPACITY;
            
            // Convert hex to RGB
            const hexColor = color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);
            
            // Create realistic laser beam with multiple layers
            
            // 1. Outer atmospheric glow (very faint, wide)
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.05})`;
            ctx.lineWidth = width * 4;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 2. Medium atmospheric scattering
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.15})`;
            ctx.lineWidth = width * 2.5;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 3. Inner beam glow
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.4})`;
            ctx.lineWidth = width * 1.5;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 4. Core laser beam (brightest, sharpest)
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.9})`;
            ctx.lineWidth = width;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 5. Intense white-hot core center
            ctx.strokeStyle = `rgba(255, 255, 255, ${opacity / 255 * 0.6})`;
            ctx.lineWidth = Math.max(1, width * 0.4);
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 6. Add electric arc effects along the beam for danger
            if (this.settings.electricArcs !== false) {
                this.drawElectricArcs(ctx, length, r, g, b, opacity);
            }
        }

        drawLaserGlow(ctx, length) {
            const width = this.settings.width || LASER_WIDTH;
            const glowSize = this.settings.glowSize || LASER_GLOW_SIZE;
            const color = this.settings.color || LASER_COLOR;
            
            // Convert hex to RGB
            const hexColor = color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);
            
            // Create realistic atmospheric scattering effect
            // This simulates how laser light interacts with air particles
            
            // 1. Wide, very faint atmospheric glow
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.02)`;
            ctx.lineWidth = width + glowSize * 3;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 2. Medium atmospheric scattering
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.04)`;
            ctx.lineWidth = width + glowSize * 2;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 3. Closer atmospheric effect
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.08)`;
            ctx.lineWidth = width + glowSize;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // Add animated particles along the laser
            if (this.settings.particles) {
                this.drawLaserParticles(ctx, length, color);
            }
        }
        
        drawLaserParticles(ctx, length, color) {
            const hexColor = color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);
            
            // Simulate atmospheric dust particles that scatter laser light
            const particleCount = Math.floor(length / 15); // More particles for realism
            
            for (let i = 0; i < particleCount; i++) {
                const x = (i + 1) * (length / (particleCount + 1));
                
                // Realistic particle distribution - more particles near the beam center
                const distanceFromCenter = Math.abs(Math.random() - 0.5) * 12;
                const y = (Math.random() - 0.5) * distanceFromCenter;
                
                // Particle size based on distance from center (smaller particles further out)
                const maxSize = Math.max(1, 4 - (distanceFromCenter / 3));
                const size = Math.random() * maxSize + 0.5;
                
                // Alpha based on distance and size (smaller, distant particles are fainter)
                const distanceFactor = Math.max(0, 1 - (distanceFromCenter / 15));
                const sizeFactor = size / maxSize;
                const alpha = (distanceFactor * sizeFactor * 0.8) + 0.1;
                
                // Draw the particle
                ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${alpha})`;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
                
                // Add a subtle white highlight to some particles (simulating light reflection)
                if (Math.random() < 0.3) {
                    ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.6})`;
                    ctx.beginPath();
                    ctx.arc(x, y, size * 0.4, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
        }
        
        drawElectricArcs(ctx, length, r, g, b, opacity) {
            // Draw dangerous electric arcs along the laser beam using jagged segments like the sparking plugin
            const arcCount = 4; // More arcs for more danger
            const arcIntensity = opacity / 255 * 0.9; // Brighter for visibility
            
            for (let i = 0; i < arcCount; i++) {
                const x = (i + 1) * (length / (arcCount + 1));
                const arcLength = 12 + Math.random() * 8; // 12-20 pixel arcs
                const arcHeight = 10 + Math.random() * 8; // 10-18 pixel height
                const segments = 4 + Math.floor(Math.random() * 3); // 4-6 jagged segments
                
                // Draw jagged white core arc
                ctx.strokeStyle = `rgba(255, 255, 255, ${arcIntensity})`;
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                
                // Create jagged path like the sparking plugin - centered on laser line
                const startX = x - arcLength / 2;
                const startY = 0; // Center on laser line (y=0)
                const endX = x + arcLength / 2;
                const endY = 0; // Center on laser line (y=0)
                
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                
                // Create jagged segments for realistic electric arc
                for (let j = 1; j < segments; j++) {
                    const t = j / segments;
                    const baseX = startX + (endX - startX) * t;
                    const baseY = startY + (endY - startY) * t;
                    
                    // Add random jaggedness perpendicular to the arc (like the sparking plugin)
                    const jag = (Math.random() - 0.5) * 8; // Random offset for jaggedness
                    const jagX = baseX;
                    const jagY = baseY + jag;
                    
                    ctx.lineTo(jagX, jagY);
                }
                
                ctx.lineTo(endX, endY);
                ctx.stroke();
                
                // Add colored glow around the white core (like the sparking plugin)
                ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${arcIntensity * 0.7})`;
                ctx.lineWidth = 4;
                ctx.stroke();
                
                // Add smaller secondary arcs for extra menace
                if (Math.random() < 0.6) { // 60% chance like the sparking plugin
                    ctx.strokeStyle = `rgba(255, 255, 255, ${arcIntensity * 0.5})`;
                    ctx.lineWidth = 1;
                    ctx.lineCap = 'round';
                    
                    const smallArcLength = arcLength * 0.6;
                    const smallArcHeight = arcHeight * 0.7;
                    
                    ctx.beginPath();
                    ctx.moveTo(x - smallArcLength/2, 0); // Center on laser line
                    
                    // Create jagged segments for small arc too
                    for (let j = 1; j < 3; j++) {
                        const t = j / 3;
                        const baseX = x - smallArcLength/2 + (smallArcLength) * t;
                        const baseY = 0; // Center on laser line
                        
                        const jag = (Math.random() - 0.5) * 4;
                        const jagX = baseX;
                        const jagY = baseY + jag;
                        
                        ctx.lineTo(jagX, jagY);
                    }
                    
                    ctx.lineTo(x + smallArcLength/2, 0); // Center on laser line
                    ctx.stroke();
                }
            }
        }

        update() {
            super.update();
            
            // Update pulse effect
            if (this.settings.pulse !== false) {
                this.pulseTime += LASER_PULSE_SPEED;
                const pulseFactor = 0.7 + Math.sin(this.pulseTime) * 0.3;
                this.opacity = (this.settings.opacity || LASER_OPACITY) * pulseFactor;
            }
            
            // Update glow animation
            if (this.settings.glow && this.settings.particles) {
                this.glowTime = (this.glowTime || 0) + 0.05;
                const glowFactor = 0.8 + Math.sin(this.glowTime) * 0.2;
                this.scale.x = glowFactor;
                this.scale.y = glowFactor;
            }
            
            // Update floating animation
            if (this.settings.float) {
                this.floatTime = (this.floatTime || 0) + 0.03;
                const floatOffset = Math.sin(this.floatTime) * 2;
                this.y = this.originalY + floatOffset;
            }
            
            // Add subtle heat distortion effect
            this.heatTime = (this.heatTime || 0) + 0.08;
            const heatDistortion = Math.sin(this.heatTime) * 0.5;
            this.skew.x = heatDistortion * 0.01; // Very subtle skew effect
            
            // Add dangerous flicker effect
            if (this.settings.flicker !== false) {
                this.flickerTime = (this.flickerTime || 0) + 0.15;
                const flickerIntensity = LASER_FLICKER_INTENSITY;
                const flickerFactor = 1 + Math.sin(this.flickerTime * 3) * flickerIntensity;
                this.opacity = (this.settings.opacity || LASER_OPACITY) * flickerFactor;
            }
            
            // Add electric arc animation with rapid flickering like the sparking plugin
            if (this.settings.electricArcs !== false) {
                this.arcTime = (this.arcTime || 0) + 0.12;
                // Subtle movement to make arcs seem alive
                this.rotation = Math.sin(this.arcTime) * 0.02;
                
                // Rapid flickering for electric effect (like the sparking plugin)
                this.visible = Math.random() > 0.3; // Flickers 70% of the time for more visibility
            }
            
            // Add warning pulse effect (red flash)
            if (this.settings.warningPulse !== false) {
                this.warningTime = (this.warningTime || 0) + 0.08;
                const warningFactor = 0.8 + Math.sin(this.warningTime * 2) * 0.2;
                // Flash between normal color and warning red
                if (Math.sin(this.warningTime) > 0) {
                    this.tint = 0xff0000; // Red warning tint
                } else {
                    this.tint = 0xffffff; // Normal color
                }
            }
        }
    }

    //=============================================================================
    // Laser Manager
    //=============================================================================
    class LaserManager {
        constructor(scene) {
            this.scene = scene;
            this.activeLasers = [];
            this.laserEvents = new Map();
            this.lastScanFrame = -1; // Track when we last scanned for performance
            this.scanInterval = 10; // Only scan every 10 frames instead of every frame
            this.frameCount = 0;

            // Create laser layer
            this.laserLayer = new Sprite();
            this.laserLayer.z = 2; // Between map tiles and characters

            // Try to add to the map layer specifically to get below characters
            if (scene._spriteset && scene._spriteset._tilemap) {
                // Add as child of the tilemap (map layer) to ensure it's below characters
                scene._spriteset._tilemap.addChild(this.laserLayer);
                console.log('EventLaserLines: Laser layer added to tilemap (below characters)');
            } else if (scene._spriteset) {
                // Fallback to spriteset
                scene._spriteset.addChild(this.laserLayer);
                console.log('EventLaserLines: Laser layer added to spriteset (fallback)');
            }
        }

        initialize() {
            // Scan for laser events immediately
            this.scanEventsForLasers();
            console.log('EventLaserLines: LaserManager initialized');
        }

        scanEventsForLasers() {
            this.laserEvents.clear();

            // Cache events array to avoid repeated function calls
            const events = $gameMap.events();
            const eventsLength = events.length;

            // Scan all events for laser notetags
            for (let index = 0; index < eventsLength; index++) {
                const event = events[index];
                if (!event || !event.event()) continue;

                const eventData = event.event();
                let allNotetags = [];

                // Check event note field (always active regardless of page)
                if (eventData.note) {
                    const notetagsFromNote = this.parseLaserNotetags(eventData.note);
                    if (notetagsFromNote.length > 0) {
                        allNotetags = allNotetags.concat(notetagsFromNote);
                    }
                }

                // Check Comment commands ONLY in the currently active page
                if (eventData.pages && eventData.pages.length > 0) {
                    // Get the current page index for this event
                    const currentPageIndex = this.getCurrentPageIndex(event);
                    if (currentPageIndex >= 0 && currentPageIndex < eventData.pages.length) {
                        const currentPage = eventData.pages[currentPageIndex];
                        const commentText = this.extractCommentText(currentPage.list);
                        if (commentText) {
                            const notetagsFromComments = this.parseLaserNotetags(commentText);
                            if (notetagsFromComments.length > 0) {
                                allNotetags = allNotetags.concat(notetagsFromComments);
                            }
                        }
                    }
                }

                if (allNotetags.length > 0) {
                    // Use the actual event ID (index + 1) instead of array index
                    const eventId = index + 1;
                    this.laserEvents.set(eventId, allNotetags);
                    // Reduced logging to prevent console spam during frequent updates
                    // console.log(`EventLaserLines: Found ${allNotetags.length} laser notetags on event ${eventId} (array index ${index})`);
                }
            }

            // Reduced logging to prevent console spam during frequent updates
            // console.log('EventLaserLines: Total laser events found:', this.laserEvents.size);
        }

        getCurrentPageIndex(event) {
            // Use RPG Maker's built-in method to get the current page index
            // This respects all the page conditions (switches, variables, etc.)
            if (event && event.findProperPageIndex) {
                return event.findProperPageIndex();
            }

            // Fallback: manually check page conditions
            const eventData = event.event();
            if (!eventData || !eventData.pages) return -1;

            for (let i = eventData.pages.length - 1; i >= 0; i--) {
                const page = eventData.pages[i];
                if (this.meetsConditions(page.conditions, event)) {
                    return i;
                }
            }

            return -1;
        }

        meetsConditions(conditions, event) {
            // Check if page conditions are met
            if (!conditions) return true;

            // Check switch conditions
            if (conditions.switch1Valid && !$gameSwitches.value(conditions.switch1Id)) {
                return false;
            }
            if (conditions.switch2Valid && !$gameSwitches.value(conditions.switch2Id)) {
                return false;
            }

            // Check variable conditions
            if (conditions.variableValid) {
                const variableValue = $gameVariables.value(conditions.variableId);
                if (variableValue < conditions.variableValue) {
                    return false;
                }
            }

            // Check self switch conditions
            if (conditions.selfSwitchValid && event) {
                const key = [$gameMap.mapId(), event._eventId, conditions.selfSwitchCh];
                if (!$gameSelfSwitches.value(key)) {
                    return false;
                }
            }

            return true;
        }

        extractCommentText(commandList) {
            let commentText = '';

            if (!commandList || !Array.isArray(commandList)) {
                return commentText;
            }

            for (let i = 0; i < commandList.length; i++) {
                const command = commandList[i];

                // Event command code 108 = Comment (first line)
                // Event command code 408 = Comment (continuation lines)
                if (command.code === 108 || command.code === 408) {
                    if (command.parameters && command.parameters[0]) {
                        commentText += command.parameters[0] + '\n';
                    }
                }
            }

            return commentText;
        }

        parseLaserNotetags(note) {
            const notetags = [];

            // Match both <laser> and <laser:...> formats
            const matches = note.match(/<laser(?:\s+(\d+))?(?::(.*?))?>/g);
            if (!matches) return notetags;

            const matchesLength = matches.length;
            for (let i = 0; i < matchesLength; i++) {
                const match = matches[i];
                const fullMatch = match.match(/<laser(?:\s+(\d+))?(?::(.*?))?>/);
                if (!fullMatch) continue;

                const laserId = fullMatch[1] ? Number(fullMatch[1]) : 1;
                const params = fullMatch[2] || '';

                // Create settings object with defaults
                const settings = {
                    direction: 'right',
                    length: 100,
                    color: LASER_COLOR,
                    width: LASER_WIDTH,
                    opacity: LASER_OPACITY,
                    pulse: LASER_PULSE,
                    glow: LASER_GLOW,
                    glowSize: LASER_GLOW_SIZE,
                    float: LASER_FLOAT,
                    particles: LASER_PARTICLES
                };

                // Parse custom parameters more efficiently
                if (params) {
                    const paramPairs = params.split(',');
                    const paramPairsLength = paramPairs.length;

                    for (let j = 0; j < paramPairsLength; j++) {
                        const param = paramPairs[j].trim();
                        const equalIndex = param.indexOf('=');
                        if (equalIndex === -1) continue;

                        const key = param.substring(0, equalIndex).trim();
                        const value = param.substring(equalIndex + 1).trim();

                        // Use switch for better performance than multiple if-else
                        switch (key) {
                            case 'direction':
                                settings.direction = value;
                                break;
                            case 'length':
                                settings.length = Number(value) || settings.length;
                                break;
                            case 'color':
                                settings.color = value;
                                break;
                            case 'width':
                                settings.width = Number(value) || settings.width;
                                break;
                            case 'opacity':
                                settings.opacity = Number(value) || settings.opacity;
                                break;
                            case 'pulse':
                                settings.pulse = value === 'true';
                                break;
                            case 'glow':
                                settings.glow = value === 'true';
                                break;
                            case 'glowSize':
                                settings.glowSize = Number(value) || settings.glowSize;
                                break;
                            case 'float':
                                settings.float = value === 'true';
                                break;
                            case 'particles':
                                settings.particles = value === 'true';
                                break;
                        }
                    }
                }

                notetags.push({
                    laserId: laserId,
                    settings: settings
                });
            }

            return notetags;
        }

        updateLasers() {
            this.frameCount++;

            // Only scan for laser events periodically to improve performance
            const shouldScan = this.frameCount - this.lastScanFrame >= this.scanInterval;

            if (shouldScan) {
                this.lastScanFrame = this.frameCount;

                // Clear old lasers
                this.clearActiveLasers();

                // Re-scan events to get current page laser notetags
                this.scanEventsForLasers();

                // Create new lasers based on current laser events
                this.createLasersFromEvents();
            } else {
                // Just update positions of existing lasers for smooth movement
                this.updateLaserPositions();
            }
        }

        clearActiveLasers() {
            this.activeLasers.forEach(laser => {
                if (laser.parent) {
                    laser.parent.removeChild(laser);
                }
            });
            this.activeLasers.length = 0; // More efficient than = []
        }

        createLasersFromEvents() {
            this.laserEvents.forEach((notetags, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) return;

                // Get event sprite position
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) return;

                const eventX = sprite.x;
                // Adjust Y to center of tile instead of bottom
                // RPG Maker tiles are 48x48 pixels, and sprite Y is at bottom of tile
                const eventY = sprite.y - ($gameMap.tileHeight() / 2);

                // Create laser for each notetag
                notetags.forEach(notetag => {
                    try {
                        const laser = new LaserLine(
                            eventX,
                            eventY,
                            notetag.settings.direction,
                            notetag.settings.length,
                            notetag.settings
                        );

                        // Position laser at event location
                        laser.x = eventX;
                        laser.y = eventY;

                        // Store reference to source event for position updates
                        laser._sourceEventId = eventId;

                        // Add to active lasers and layer
                        this.activeLasers.push(laser);
                        this.laserLayer.addChild(laser);

                        // Reduced logging to prevent console spam during frequent updates
                        // console.log(`EventLaserLines: Created laser ${notetag.laserId} at (${eventX}, ${eventY})`);
                    } catch (error) {
                        console.error(`EventLaserLines: Error creating laser ${notetag.laserId}:`, error);
                    }
                });
            });
        }

        updateLaserPositions() {
            // Update positions of existing lasers without recreating them
            this.activeLasers.forEach(laser => {
                if (laser._sourceEventId) {
                    const event = $gameMap.event(laser._sourceEventId);
                    if (event) {
                        const sprite = this.scene._spriteset.findTargetSprite(event);
                        if (sprite) {
                            laser.x = sprite.x;
                            laser.y = sprite.y - ($gameMap.tileHeight() / 2);
                        }
                    }
                }
            });
        }

        destroy() {
            // Clean up active lasers
            this.activeLasers.forEach(laser => {
                if (laser.parent) {
                    laser.parent.removeChild(laser);
                }
            });
            this.activeLasers = [];
            this.laserEvents.clear();
            
            // Clean up laser layer
            if (this.laserLayer && this.laserLayer.parent) {
                this.laserLayer.parent.removeChild(this.laserLayer);
            }
            if (this.laserLayer) {
                this.laserLayer.destroy();
            }
        }
    }

    //=============================================================================
    // Scene_Map Integration
    //=============================================================================
    const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
    Scene_Map.prototype.createSpriteset = function() {
        _Scene_Map_createSpriteset.call(this);
        this._laserManager = new LaserManager(this);
        this._laserManager.initialize();
    };

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);
        if (this._laserManager) {
            this._laserManager.updateLasers();
        }
    };

    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        if (this._laserManager) {
            this._laserManager.destroy();
        }
        _Scene_Map_terminate.call(this);
    };

    console.log('EventLaserLines: Plugin loaded successfully');

})();
