#!/usr/bin/env python3
"""
Stat Growth Pattern Analyzer for Trials of Cascadia
Analyzes how weapon stats (ATK, DEF, CRIT) scale across tiers and materials
"""

import json
from collections import defaultdict

def load_weapons(filepath="../data/Weapons.json"):
    """Load weapons data from JSON file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading weapons: {e}")
        return None

def analyze_stat_growth(weapons_data):
    """Analyze stat growth patterns across weapon tiers and materials."""
    
    # Define weapon type mappings
    weapon_types = {
        "Swords": {"wtypeId": 2, "animationId": 6},
        "Spears": {"wtypeId": 12, "animationId": 11},
        "Daggers": {"wtypeId": 1, "animationId": 225},
        "Staves": {"wtypeId": 6, "animationId": 333}
    }
    
    # Organize weapons by type and analyze stats
    stat_analysis = {}
    
    for weapon_type, info in weapon_types.items():
        print(f"\n{'='*60}")
        print(f"ANALYZING {weapon_type.upper()} STAT GROWTH")
        print(f"{'='*60}")
        
        # Get weapons of this type
        weapons = [w for w in weapons_data if w and w.get("wtypeId") == info["wtypeId"]]
        
        if not weapons:
            continue
        
        # Analyze stat progression
        tier_stats = analyze_weapon_tier_stats(weapons)
        material_stats = analyze_material_stats(weapons)
        stat_scaling = analyze_stat_scaling(weapons)
        
        stat_analysis[weapon_type] = {
            'tier_stats': tier_stats,
            'material_stats': material_stats,
            'stat_scaling': stat_scaling,
            'weapons': weapons
        }
        
        # Print analysis
        print_stat_analysis(weapon_type, tier_stats, material_stats, stat_scaling)
    
    return stat_analysis

def analyze_weapon_tier_stats(weapons):
    """Analyze how stats scale across weapon tiers."""
    
    tiers = {
        'Basic': {'Copper': [], 'Bronze': [], 'Silver': []},
        'Advanced': {'Gold': [], 'Platinum': []},
        'Gemstone': {'Sapphire': [], 'Ruby': [], 'Emerald': [], 'Amethyst': []},
        'Unique': [],
        'Legendary': []
    }
    
    for weapon in weapons:
        name = weapon.get("name", "")
        params = weapon.get("params", [0, 0, 0, 0, 0, 0, 0, 0])
        price = weapon.get("price", 0)
        
        # Extract stats
        atk = params[2] if len(params) > 2 else 0
        def_val = params[3] if len(params) > 3 else 0
        crit = params[4] if len(params) > 4 else 0
        
        # Categorize by tier
        if any(material in name for material in ["Copper", "Bronze", "Silver"]):
            for material in tiers['Basic']:
                if material in name:
                    tiers['Basic'][material].append({
                        'name': name, 'atk': atk, 'def': def_val, 'crit': crit,
                        'price': price, 'total_power': atk + def_val + crit
                    })
                    break
        elif any(material in name for material in ["Gold", "Platinum"]):
            for material in tiers['Advanced']:
                if material in name:
                    tiers['Advanced'][material].append({
                        'name': name, 'atk': atk, 'def': def_val, 'crit': crit,
                        'price': price, 'total_power': atk + def_val + crit
                    })
                    break
        elif any(material in name for material in ["Sapphire", "Ruby", "Emerald", "Amethyst"]):
            for material in tiers['Gemstone']:
                if material in name:
                    tiers['Gemstone'][material].append({
                        'name': name, 'atk': atk, 'def': def_val, 'crit': crit,
                        'price': price, 'total_power': atk + def_val + crit
                    })
                    break
        elif price == 0:
            if atk > 100:  # High attack = legendary
                tiers['Legendary'].append({
                    'name': name, 'atk': atk, 'def': def_val, 'crit': crit,
                    'price': price, 'total_power': atk + def_val + crit
                })
            else:
                tiers['Unique'].append({
                    'name': name, 'atk': atk, 'def': def_val, 'crit': crit,
                    'price': price, 'total_power': atk + def_val + crit
                })
    
    return tiers

def analyze_material_stats(weapons):
    """Analyze how stats scale across materials."""
    
    materials = ['Copper', 'Bronze', 'Silver', 'Gold', 'Platinum', 
                'Sapphire', 'Ruby', 'Emerald', 'Amethyst']
    
    material_stats = {}
    
    for material in materials:
        material_weapons = [w for w in weapons if material in w.get("name", "")]
        if material_weapons:
            atk_values = [w.get("params", [0,0,0,0,0,0,0,0])[2] for w in material_weapons]
            def_values = [w.get("params", [0,0,0,0,0,0,0,0])[3] for w in material_weapons]
            crit_values = [w.get("params", [0,0,0,0,0,0,0,0])[4] for w in material_weapons]
            prices = [w.get("price", 0) for w in material_weapons]
            
            material_stats[material] = {
                'count': len(material_weapons),
                'avg_atk': sum(atk_values) / len(atk_values) if atk_values else 0,
                'avg_def': sum(def_values) / len(def_values) if def_values else 0,
                'avg_crit': sum(crit_values) / len(crit_values) if crit_values else 0,
                'avg_price': sum(prices) / len(prices) if prices else 0,
                'total_power': sum(atk_values) + sum(def_values) + sum(crit_values)
            }
    
    return material_stats

def analyze_stat_scaling(weapons):
    """Analyze how stats scale with price and power."""
    
    scaling_data = []
    
    for weapon in weapons:
        params = weapon.get("params", [0, 0, 0, 0, 0, 0, 0, 0])
        price = weapon.get("price", 0)
        
        if len(params) >= 5 and price > 0:
            atk = params[2]
            def_val = params[3]
            crit = params[4]
            total_power = atk + def_val + crit
            
            scaling_data.append({
                'price': price,
                'atk': atk,
                'def': def_val,
                'crit': crit,
                'total_power': total_power,
                'power_per_gold': total_power / price if price > 0 else 0
            })
    
    # Calculate scaling ratios
    if scaling_data:
        avg_power_per_gold = sum(d['power_per_gold'] for d in scaling_data) / len(scaling_data)
        price_efficiency = sorted(scaling_data, key=lambda x: x['power_per_gold'], reverse=True)
        
        return {
            'avg_power_per_gold': avg_power_per_gold,
            'most_efficient': price_efficiency[:3],
            'least_efficient': price_efficiency[-3:],
            'scaling_data': scaling_data
        }
    
    return {}

def print_stat_analysis(weapon_type, tier_stats, material_stats, stat_scaling):
    """Print comprehensive stat analysis."""
    
    print(f"\n📊 TIER-BASED STAT ANALYSIS:")
    print("-" * 40)
    
    for tier_name, tier_data in tier_stats.items():
        if tier_name in ['Basic', 'Advanced', 'Gemstone']:
            print(f"\n  {tier_name} Tier:")
            for material, weapons in tier_data.items():
                if weapons:
                    weapon = weapons[0]  # Should only be one per material
                    print(f"    {material}: ATK {weapon['atk']}, DEF {weapon['def']}, CRIT {weapon['crit']}")
        elif tier_data:  # Unique and Legendary
            print(f"\n  {tier_name} Tier:")
            for weapon in tier_data:
                print(f"    {weapon['name']}: ATK {weapon['atk']}, DEF {weapon['def']}, CRIT {weapon['crit']}")
    
    print(f"\n🏗️  MATERIAL-BASED STAT SCALING:")
    print("-" * 40)
    
    if material_stats:
        # Sort by average attack power
        sorted_materials = sorted(material_stats.items(), key=lambda x: x[1]['avg_atk'])
        
        for material, stats in sorted_materials:
            print(f"  {material}: ATK {stats['avg_atk']:.1f}, DEF {stats['avg_def']:.1f}, "
                  f"CRIT {stats['avg_crit']:.1f}, Price {stats['avg_price']:,.0f}")
    
    print(f"\n💰 PRICE EFFICIENCY ANALYSIS:")
    print("-" * 40)
    
    if stat_scaling:
        print(f"  Average Power per Gold: {stat_scaling['avg_power_per_gold']:.3f}")
        
        if 'most_efficient' in stat_scaling:
            print(f"\n  Most Cost-Effective Weapons:")
            for weapon in stat_scaling['most_efficient']:
                print(f"    {weapon['total_power']} power for {weapon['price']:,} gold "
                      f"({weapon['power_per_gold']:.3f} power/gold)")
        
        if 'least_efficient' in stat_scaling:
            print(f"\n  Least Cost-Effective Weapons:")
            for weapon in stat_scaling['least_efficient']:
                print(f"    {weapon['total_power']} power for {weapon['price']:,} gold "
                      f"({weapon['power_per_gold']:.3f} power/gold)")

def generate_stat_growth_report(stat_analysis):
    """Generate a comprehensive stat growth report."""
    
    print(f"\n{'='*80}")
    print("COMPREHENSIVE STAT GROWTH REPORT")
    print(f"{'='*80}")
    
    # Cross-type stat comparison
    print("\n📈 CROSS-TYPE STAT COMPARISON:")
    print("-" * 50)
    
    for weapon_type, analysis in stat_analysis.items():
        weapons = analysis['weapons']
        if weapons:
            atk_values = [w.get("params", [0,0,0,0,0,0,0,0])[2] for w in weapons if w.get("params")]
            def_values = [w.get("params", [0,0,0,0,0,0,0,0])[3] for w in weapons if w.get("params")]
            crit_values = [w.get("params", [0,0,0,0,0,0,0,0])[4] for w in weapons if w.get("params")]
            
            if atk_values:
                print(f"\n  {weapon_type}:")
                print(f"    ATK: {min(atk_values)} → {max(atk_values)} (avg: {sum(atk_values)/len(atk_values):.1f})")
                if def_values:
                    print(f"    DEF: {min(def_values)} → {max(def_values)} (avg: {sum(def_values)/len(def_values):.1f})")
                if crit_values:
                    print(f"    CRIT: {min(crit_values)} → {max(crit_values)} (avg: {sum(crit_values)/len(crit_values):.1f})")
    
    # Growth curve analysis
    print(f"\n📊 GROWTH CURVE ANALYSIS:")
    print("-" * 50)
    
    for weapon_type, analysis in stat_analysis.items():
        material_stats = analysis['material_stats']
        if material_stats:
            print(f"\n  {weapon_type} Material Progression:")
            
            # Sort materials by progression order
            progression_order = ['Copper', 'Bronze', 'Silver', 'Gold', 'Platinum']
            for material in progression_order:
                if material in material_stats:
                    stats = material_stats[material]
                    print(f"    {material}: ATK {stats['avg_atk']:.1f} → "
                          f"DEF {stats['avg_def']:.1f} → CRIT {stats['avg_crit']:.1f}")
    
    # Efficiency recommendations
    print(f"\n💡 EFFICIENCY RECOMMENDATIONS:")
    print("-" * 50)
    
    for weapon_type, analysis in stat_analysis.items():
        if 'most_efficient' in analysis['stat_scaling']:
            best_weapon = analysis['stat_scaling']['most_efficient'][0]
            print(f"\n  {weapon_type} - Best Value:")
            print(f"    {best_weapon['total_power']} total power for {best_weapon['price']:,} gold")
            print(f"    Efficiency: {best_weapon['power_per_gold']:.3f} power per gold")

def export_stat_analysis(stat_analysis, filename="stat_growth_analysis.json"):
    """Export stat analysis to JSON file."""
    
    export_data = {}
    
    for weapon_type, analysis in stat_analysis.items():
        export_data[weapon_type] = {
            'tier_stats': {
                tier: {
                    material: [{'name': w['name'], 'atk': w['atk'], 'def': w['def'], 
                              'crit': w['crit'], 'price': w['price']} for w in weapons]
                    for material, weapons in tier_data.items()
                } if isinstance(tier_data, dict) else 
                [{'name': w['name'], 'atk': w['atk'], 'def': w['def'], 
                  'crit': w['crit'], 'price': w['price']} for w in tier_data]
                for tier, tier_data in analysis['tier_stats'].items()
            },
            'material_stats': analysis['material_stats'],
            'stat_scaling': {
                'avg_power_per_gold': analysis['stat_scaling'].get('avg_power_per_gold', 0),
                'most_efficient': analysis['stat_scaling'].get('most_efficient', []),
                'least_efficient': analysis['stat_scaling'].get('least_efficient', [])
            }
        }
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        print(f"\n✓ Stat analysis exported to {filename}")
        return True
    except Exception as e:
        print(f"\n✗ Error exporting analysis: {e}")
        return False

def main():
    """Main function to run the stat growth analysis."""
    
    print("TRIALS OF CASCADIA - STAT GROWTH PATTERN ANALYSIS")
    print("Analyzing ATK, DEF, and CRIT scaling across weapon tiers and materials")
    print("=" * 80)
    
    # Load weapons data
    weapons_data = load_weapons()
    if not weapons_data:
        print("Failed to load weapons data. Exiting.")
        return
    
    print(f"Loaded {len(weapons_data)} weapons from database")
    
    # Analyze stat growth patterns
    stat_analysis = analyze_stat_growth(weapons_data)
    
    # Generate comprehensive report
    generate_stat_growth_report(stat_analysis)
    
    # Export results
    export_stat_analysis(stat_analysis)
    
    print(f"\n{'='*80}")
    print("STAT GROWTH ANALYSIS COMPLETE!")
    print("Check 'stat_growth_analysis.json' for detailed results")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
