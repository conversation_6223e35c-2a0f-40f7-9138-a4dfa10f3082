/*:
 * @target MZ
 * @plugindesc Disables mouse-based character movement while retaining other mouse functionalities.
 *
 * @help
 * This plugin disables the ability to move the player character using mouse clicks on the map.
 * Other mouse interactions, such as menu navigation and event triggering, remain functional.
 */

(() => {
    // Override the default setDestination method to prevent mouse-based movement
    Game_Temp.prototype.setDestination = function (x, y) {
        // Do nothing, effectively disabling click-to-move
    };
})();
