/*:============================================================================
*
* @target MZ
* @plugindesc Cloud Number Window - Animates 7 random cloud digits across the window and stores the number in v11.
*
* @help
* Plugin Command:
*   ShowCloudNumberWindow
*     - Generates 7 random digits (1-9), stores as string in v11, animates cloud1-9.png (pictures 11-17) from right to left across the window.
*
* @param startX
* @text Start X
* @type number
* @default 900
* @desc X position where clouds start (off-screen right)
*
* @param endX
* @text End X
* @type number
* @default 200
* @desc X position where first cloud ends (window left)
*
* @param y
* @text Y Position
* @type number
* @default 200
* @desc Y position for clouds
*
* @param spacing
* @text Cloud Spacing
* @type number
* @default 60
* @desc Horizontal spacing between clouds
*
* @param moveDuration
* @text Move Duration (frames)
* @type number
* @default 120
* @desc Frames for each cloud to move across the window
*
* @param delayBetween
* @text Delay Between Clouds (frames)
* @type number
* @default 30
* @desc Frames to wait before starting next cloud
*
* @command ShowCloudNumberWindow
* @text Show Cloud Number Window
* @desc Generates 7 random digits (1-9), stores as string in v11, animates cloud1-9.png (pictures 11-17) from right to left across the window.
*/

(() => {
    // Simple seeded random number generator
    function seededRandom(seed) {
        let m = 0x80000000;
        let a = 1103515245;
        let c = 12345;
        let state = seed ? seed : Math.floor(Math.random() * (m - 1));
        return function() {
            state = (a * state + c) % m;
            return state / (m - 1);
        };
    }

    // Track all timeout IDs for cleanup
    if (!window._cloudNumberTimeouts) window._cloudNumberTimeouts = [];

    const pluginName = "CloudNumberWindow";
    const parameters = PluginManager.parameters(pluginName);
    const startX = Number(parameters.startX || 900);
    const endX = Number(parameters.endX || 200);
    const y = Number(parameters.y || 200);
    const spacing = Number(parameters.spacing || 60);
    const moveDuration = Number(parameters.moveDuration || 120);
    const delayBetween = Number(parameters.delayBetween || 30);

    // Cleanup function to clear all timeouts and erase cloud pictures
    function cleanupCloudNumbers() {
        if (window._cloudNumberTimeouts) {
            window._cloudNumberTimeouts.forEach(id => clearTimeout(id));
            window._cloudNumberTimeouts = [];
        }
        if (window.$gameScreen) {
            for (let i = 11; i <= 17; i++) {
                $gameScreen.erasePicture(i);
            }
        }
    }

    // Hook into Scene_Map.terminate to always clean up on map transfer
    const _CloudNumberWindow_SceneMapTerminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        cleanupCloudNumbers();
        _CloudNumberWindow_SceneMapTerminate.call(this);
    };

    PluginManager.registerCommand(pluginName, "ShowCloudNumberWindow", args => {
        // Generate 7 digits (1-9) using save-specific seed
        const digits = [];
        // Use the same RNG seed system as RNGSeedPlugin
        const seed = window._rngSeed || Date.now();
        const rng = seededRandom(seed);
        for (let i = 0; i < 7; i++) {
            digits[i] = Math.floor(rng() * 9) + 1;
        }
        // Store as a number (not string) to match Input Number command format
        $gameVariables.setValue(11, Number(digits.join("")));
        // Erase any existing cloud pictures
        for (let i = 0; i < 7; i++) {
            $gameScreen.erasePicture(11 + i);
        }
        // Animate clouds
        for (let i = 0; i < 7; i++) {
            const picNum = 11 + i;
            const digit = digits[i];
            const targetX = endX;
            const showTimeout = setTimeout(() => {
                $gameScreen.showPicture(picNum, 'cloud' + digit, 0, startX, y, 100, 100, 255, 0);
                $gameScreen.movePicture(picNum, 0, targetX, y, 100, 100, 255, 0, moveDuration);
                // Erase this cloud after it finishes moving + buffer (60 frames)
                const eraseTimeout = setTimeout(() => {
                    $gameScreen.erasePicture(picNum);
                }, (moveDuration + 60) * 16.67);
                window._cloudNumberTimeouts.push(eraseTimeout);
            }, i * delayBetween * 16.67);
            window._cloudNumberTimeouts.push(showTimeout);
        }
    });
})(); 